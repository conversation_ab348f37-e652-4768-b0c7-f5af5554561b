import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { Formik } from 'formik';
import * as yup from 'yup';
import axios from 'axios';

const EditCollecteModal = ({ open, onClose, collecte, onCollecteUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pointsRamassage, setPointsRamassage] = useState([]);

  // Charger les points de ramassage
  useEffect(() => {
    const fetchPointsRamassage = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/points', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setPointsRamassage(response.data.points || response.data || []);
      } catch (error) {
        console.error('Erreur lors du chargement des points de ramassage:', error);
      }
    };

    if (open) {
      fetchPointsRamassage();
    }
  }, [open]);

  // Schéma de validation
  const collecteSchema = yup.object().shape({
    id_point_ramassage: yup.number().required('Le point de ramassage est requis'),
    date_ramassage: yup.string().required('La date de ramassage est requise'),
    type_coquillage: yup.string().required('Le type de coquillage est requis'),
    poids_kg: yup.number().positive('Le poids doit être positif').required('Le poids est requis'),
    montant_achat: yup.number().positive('Le montant doit être positif').required('Le montant est requis'),
    statut_collecte: yup.string().required('Le statut est requis')
  });

  // Valeurs initiales du formulaire
  const initialValues = {
    id_point_ramassage: collecte?.id_point_ramassage || '',
    date_ramassage: collecte?.date_ramassage ? 
      new Date(collecte.date_ramassage).toISOString().slice(0, 16) : '',
    type_coquillage: collecte?.type_coquillage || '',
    poids_kg: collecte?.poids_kg || '',
    montant_achat: collecte?.montant_achat || '',
    statut_collecte: collecte?.statut_collecte || collecte?.status || 'À faire'
  };

  const handleFormSubmit = async (values, { setSubmitting }) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      
      // Convertir la date au format MySQL
      const formattedDate = new Date(values.date_ramassage).toISOString().slice(0, 19).replace('T', ' ');
      
      const updateData = {
        ...values,
        date_ramassage: formattedDate,
        poids_kg: parseFloat(values.poids_kg),
        montant_achat: parseFloat(values.montant_achat)
      };

      const response = await axios.put(`/api/collectes/${collecte.id}`, updateData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Collecte mise à jour avec succès !');
      
      // Notifier le parent que la collecte a été mise à jour
      if (onCollecteUpdated) {
        onCollecteUpdated(response.data.collecte || response.data);
      }

      // Fermer la modale après un délai
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 1500);

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      setError(
        error.response?.data?.message || 
        'Erreur lors de la mise à jour de la collecte'
      );
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  if (!collecte) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle sx={{ 
        fontSize: '1.5rem', 
        fontWeight: 600, 
        color: '#1976d2',
        borderBottom: '1px solid #e0e0e0',
        pb: 2
      }}>
        Modifier la collecte
      </DialogTitle>

      <Formik
        onSubmit={handleFormSubmit}
        initialValues={initialValues}
        validationSchema={collecteSchema}
        enableReinitialize={true}
      >
        {({
          values,
          errors,
          touched,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent sx={{ pt: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              <Box display="grid" gap="20px">
                <FormControl fullWidth>
                  <InputLabel>Point de ramassage</InputLabel>
                  <Select
                    value={values.id_point_ramassage}
                    label="Point de ramassage"
                    name="id_point_ramassage"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.id_point_ramassage && !!errors.id_point_ramassage}
                  >
                    {pointsRamassage.map((point) => (
                      <MenuItem key={point.id} value={point.id}>
                        {point.nom_restaurant_cafe || point.nom} - {point.adresse}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  variant="outlined"
                  type="datetime-local"
                  label="Date de ramassage"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.date_ramassage}
                  name="date_ramassage"
                  error={!!touched.date_ramassage && !!errors.date_ramassage}
                  helperText={touched.date_ramassage && errors.date_ramassage}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />

                <FormControl fullWidth>
                  <InputLabel>Type de coquillage</InputLabel>
                  <Select
                    value={values.type_coquillage}
                    label="Type de coquillage"
                    name="type_coquillage"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.type_coquillage && !!errors.type_coquillage}
                  >
                    <MenuItem value="Huîtres">Huîtres</MenuItem>
                    <MenuItem value="Moules">Moules</MenuItem>
                    <MenuItem value="Palourdes">Palourdes</MenuItem>
                    <MenuItem value="Coques">Coques</MenuItem>
                    <MenuItem value="Autres">Autres</MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  variant="outlined"
                  type="number"
                  label="Poids (kg)"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.poids_kg}
                  name="poids_kg"
                  error={!!touched.poids_kg && !!errors.poids_kg}
                  helperText={touched.poids_kg && errors.poids_kg}
                  inputProps={{ step: "0.01", min: "0" }}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="number"
                  label="Montant d'achat (€)"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.montant_achat}
                  name="montant_achat"
                  error={!!touched.montant_achat && !!errors.montant_achat}
                  helperText={touched.montant_achat && errors.montant_achat}
                  inputProps={{ step: "0.01", min: "0" }}
                />

                <FormControl fullWidth>
                  <InputLabel>Statut</InputLabel>
                  <Select
                    value={values.statut_collecte}
                    label="Statut"
                    name="statut_collecte"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.statut_collecte && !!errors.statut_collecte}
                  >
                    <MenuItem value="À faire">À faire</MenuItem>
                    <MenuItem value="En attente">En attente</MenuItem>
                    <MenuItem value="Terminé">Terminé</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button 
                onClick={handleClose}
                variant="outlined"
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading || isSubmitting}
                startIcon={loading ? <CircularProgress size={20} /> : null}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': {
                    backgroundColor: '#1565c0'
                  }
                }}
              >
                {loading ? 'Mise à jour...' : 'Mettre à jour'}
              </Button>
            </DialogActions>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

export default EditCollecteModal;
