import { Box, Typography, useTheme, IconButton, CircularProgress, Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from "@mui/material";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { tokens } from "../../theme";
import Header from "../../components/Header";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import EditIcon from "@mui/icons-material/Edit";
import EditPointModal from "../../components/EditPointModal";
import { useState, useEffect } from "react";
import axios from "axios";

const Points = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const [points, setPoints] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pointToDelete, setPointToDelete] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [pointToEdit, setPointToEdit] = useState(null);

  useEffect(() => {
    fetchPoints();
  }, []);

  const fetchPoints = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get('/api/points', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const pointsData = response.data.points || response.data || [];
      setPoints(pointsData);
      setLoading(false);
    } catch (err) {
      console.error("Erreur lors de la récupération des points de ramassage:", err);
      setError("Erreur lors de la récupération des points de ramassage. Veuillez réessayer.");
      setLoading(false);
    }
  };

  const handleDeleteClick = (pointId) => {
    setPointToDelete(pointId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`/api/points/${pointToDelete}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      setPoints(points.filter(point => point.id !== pointToDelete));
      setDeleteDialogOpen(false);
      setPointToDelete(null);
    } catch (err) {
      console.error("Erreur lors de la suppression du point:", err);
      setError("Erreur lors de la suppression du point. Veuillez réessayer.");
      setDeleteDialogOpen(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setPointToDelete(null);
  };

  const handleEditClick = (point) => {
    setPointToEdit(point);
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setPointToEdit(null);
  };

  const handlePointUpdated = (updatedPoint) => {
    setPoints(points.map(point => 
      point.id === updatedPoint.id ? { ...point, ...updatedPoint } : point
    ));
  };

  const columns = [
    {
      field: "actions",
      headerName: "Actions",
      width: 150,
      renderCell: (params) => (
        <Box display="flex" gap="8px">
          <IconButton
            onClick={() => handleEditClick(params.row)}
            color="primary"
            title="Modifier"
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            onClick={() => handleDeleteClick(params.row.id)}
            color="error"
            title="Supprimer"
            size="small"
          >
            <DeleteOutlineIcon />
          </IconButton>
        </Box>
      ),
    },
    { field: "id", headerName: "ID", width: 70 },
    {
      field: "nom_restaurant_cafe",
      headerName: "Nom",
      flex: 1,
      cellClassName: "name-column--cell",
      valueGetter: (params) => params.row.nom_restaurant_cafe || params.row.nom || ""
    },
    {
      field: "adresse",
      headerName: "Adresse",
      flex: 2,
    },
    {
      field: "telephone",
      headerName: "Téléphone",
      flex: 1,
    },
    {
      field: "type_coquillage",
      headerName: "Type de coquillage",
      flex: 1,
      renderCell: ({ row }) => {
        const type = row.type_coquillage || "Non spécifié";
        return (
          <Box
            width="80%"
            m="0 auto"
            p="5px"
            display="flex"
            justifyContent="center"
            backgroundColor={
              type === "Huîtres"
                ? colors.greenAccent[600]
                : type === "Moules"
                ? colors.blueAccent[600]
                : type === "Palourdes"
                ? colors.redAccent[600]
                : colors.grey[700]
            }
            borderRadius="4px"
          >
            <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
              {type}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "created_at",
      headerName: "Date de création",
      flex: 1,
      valueGetter: (params) => {
        if (!params.row.created_at) return "";
        return new Date(params.row.created_at).toLocaleDateString('fr-FR');
      }
    },
  ];

  return (
    <Box m="20px" width="100%">
      <Header title="POINTS DE RAMASSAGE" subtitle="Gestion des points de ramassage" />

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <Typography variant="h5" color="error">{error}</Typography>
        </Box>
      ) : (
        <Box
          m="40px 0 0 0"
          height="75vh"
          sx={{
            "& .MuiDataGrid-root": {
              border: "none",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "none",
            },
            "& .name-column--cell": {
              color: colors.greenAccent[300],
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.blueAccent[700],
              borderBottom: "none",
            },
            "& .MuiDataGrid-virtualScroller": {
              backgroundColor: colors.primary[400],
            },
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              backgroundColor: colors.blueAccent[700],
            },
            "& .MuiCheckbox-root": {
              color: `${colors.greenAccent[200]} !important`,
            },
            "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
              color: `${colors.grey[100]} !important`,
            },
          }}
        >
          <DataGrid
            rows={points}
            columns={columns}
            components={{ Toolbar: GridToolbar }}
            sx={{ width: '100%' }}
          />
        </Box>
      )}

      {/* Dialogue de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer ce point de ramassage ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Annuler
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale d'édition de point */}
      <EditPointModal
        open={editModalOpen}
        onClose={handleEditClose}
        point={pointToEdit}
        onPointUpdated={handlePointUpdated}
      />
    </Box>
  );
};

export default Points;
