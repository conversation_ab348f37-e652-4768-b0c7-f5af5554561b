import { Box, Typography, useTheme, IconButton, CircularProgress, Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from "@mui/material";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { tokens } from "../../theme";
import Header from "../../components/Header";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import EditIcon from "@mui/icons-material/Edit";
import EditCollecteModal from "../../components/EditCollecteModal";
import { useState, useEffect } from "react";
import { deleteCollecte, getAllCollectes, testAuthentication } from "../../../../services/collecteService";
import { isAuthenticated } from "../../../../services/authService";

const DriverCollections = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [collectionToDelete, setCollectionToDelete] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [collecteToEdit, setCollecteToEdit] = useState(null);

  useEffect(() => {
    // Vérifier si l'utilisateur est authentifié avant de récupérer les collectes
    if (isAuthenticated()) {
      fetchCollections();
    } else {
      setError("Vous devez être connecté pour accéder à cette page.");
      setLoading(false);
    }
  }, []);

  const fetchCollections = async () => {
    try {
      console.log("Début de la récupération des collectes des chauffeurs");
      setLoading(true);
      setError(null); // Réinitialiser les erreurs précédentes

      // Vérifier si l'utilisateur est authentifié
      if (!isAuthenticated()) {
        setError("Vous devez être connecté pour accéder à cette page.");
        setLoading(false);
        return;
      }

      // Récupérer toutes les collectes
      const allData = await getAllCollectes();
      console.log("Toutes les collectes reçues:", allData.length);

      // Filtrer les collectes qui ont un id_chauffeur ET un statut "Terminé"
      const driverCollections = allData.filter(item =>
        item.id_chauffeur &&
        (item.statut_collecte === "Terminé" || item.statut === "Terminé")
      );
      console.log("Collectes terminées des chauffeurs filtrées:", driverCollections.length);

      setCollections(driverCollections);
      setLoading(false);
    } catch (err) {
      console.error("Erreur lors de la récupération des collectes des chauffeurs:", err);

      // Gérer les erreurs d'authentification spécifiquement
      if (err.message && (err.message.includes('authentifié') || err.message.includes('token') || err.message.includes('401'))) {
        setError("Session expirée ou invalide. Veuillez vous reconnecter.");
      } else {
        setError("Erreur lors de la récupération des collectes des chauffeurs. Veuillez réessayer.");
      }

      setLoading(false);
    }
  };

  const handleDeleteClick = (collectionId) => {
    setCollectionToDelete(collectionId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteCollecte(collectionToDelete);
      setCollections(collections.filter(collection => collection.id !== collectionToDelete));
      setDeleteDialogOpen(false);
      setCollectionToDelete(null);
    } catch (err) {
      console.error("Erreur lors de la suppression de la collecte:", err);
      setError("Erreur lors de la suppression de la collecte. Veuillez réessayer.");
      setDeleteDialogOpen(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCollectionToDelete(null);
  };

  const handleEditClick = (collecte) => {
    setCollecteToEdit(collecte);
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setCollecteToEdit(null);
  };

  const handleCollecteUpdated = (updatedCollecte) => {
    setCollections(collections.map(collecte =>
      collecte.id === updatedCollecte.id ? { ...collecte, ...updatedCollecte } : collecte
    ));
  };

  const columns = [
    {
      field: "actions",
      headerName: "Actions",
      width: 150,
      renderCell: (params) => (
        <Box display="flex" gap="8px">
          <IconButton
            onClick={() => handleEditClick(params.row)}
            color="primary"
            title="Modifier"
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            onClick={() => handleDeleteClick(params.row.id)}
            color="error"
            title="Supprimer"
            size="small"
          >
            <DeleteOutlineIcon />
          </IconButton>
        </Box>
      ),
    },
    { field: "id", headerName: "ID", width: 70 },
    {
      field: "id_point_ramassage",
      headerName: "ID Point",
      width: 90,
    },
    {
      field: "date_ramassage",
      headerName: "Date",
      flex: 1,
      valueGetter: (params) => {
        if (!params.row.date_ramassage) return "";
        return new Date(params.row.date_ramassage).toLocaleDateString('fr-FR');
      }
    },
    {
      field: "type_coquillage",
      headerName: "Type de Coquillage",
      flex: 1,
    },
    {
      field: "poids_kg",
      headerName: "Poids (kg)",
      flex: 0.7,
    },
    {
      field: "montant_achat",
      headerName: "Montant (DT)",
      flex: 0.7,
      renderCell: (params) => (
        <Typography color={colors.greenAccent[500]}>
          {params.row.montant_achat} DT
        </Typography>
      ),
    },
    {
      field: "statut_collecte",
      headerName: "Statut",
      flex: 0.7,
      renderCell: (params) => {
        const statut = params.row.statut_collecte || "";
        return (
          <Box
            width="80%"
            m="0 auto"
            p="5px"
            display="flex"
            justifyContent="center"
            backgroundColor={
              statut === "Terminé"
                ? colors.greenAccent[600]
                : statut === "En attente"
                ? colors.blueAccent[600]
                : statut === "À faire"
                ? colors.redAccent[600]
                : colors.grey[700]
            }
            borderRadius="4px"
          >
            <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
              {statut}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "id_chauffeur",
      headerName: "ID Chauffeur",
      flex: 0.7,
    },
    {
      field: "nom_chauffeur",
      headerName: "Nom du Chauffeur",
      flex: 1,
      valueGetter: (params) => {
        return params.row.nom_chauffeur || params.row.email_chauffeur?.split('@')[0] || "N/A";
      }
    },
    {
      field: "created_at",
      headerName: "Créé le",
      flex: 1,
      valueGetter: (params) => {
        if (!params.row.created_at) return "";
        return new Date(params.row.created_at).toLocaleDateString('fr-FR');
      }
    },
  ];

  return (
    <Box m="20px" width="100%">
      <Header title="COLLECTES TERMINÉES" subtitle="Liste des collectes terminées par les chauffeurs" />

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" height="75vh">
          <Typography variant="h5" color="error" mb={2}>{error}</Typography>
          <Box mt={2}>
            {error.includes('connect') ? (
              <Button
                variant="contained"
                color="primary"
                onClick={() => window.location.href = '/login'}
              >
                Se connecter
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                onClick={fetchCollections}
              >
                Réessayer
              </Button>
            )}
          </Box>
        </Box>
      ) : collections.length === 0 ? (
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" height="75vh">
          <Typography variant="h5" color={colors.grey[500]} mb={2}>
            Aucune collecte terminée trouvée
          </Typography>
          <Typography variant="body1" color={colors.grey[400]} mb={3}>
            Il n'y a pas encore de collectes terminées par les chauffeurs ou il y a un problème de connexion.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={fetchCollections}
          >
            Actualiser
          </Button>
        </Box>
      ) : (
        <Box
          m="40px 0 0 0"
          height="75vh"
          sx={{
            "& .MuiDataGrid-root": {
              border: "none",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "none",
            },
            "& .name-column--cell": {
              color: colors.greenAccent[300],
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.blueAccent[700],
              borderBottom: "none",
            },
            "& .MuiDataGrid-virtualScroller": {
              backgroundColor: colors.primary[400],
            },
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              backgroundColor: colors.blueAccent[700],
            },
            "& .MuiCheckbox-root": {
              color: `${colors.greenAccent[200]} !important`,
            },
            "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
              color: `${colors.grey[100]} !important`,
            },
          }}
        >
          <DataGrid
            rows={collections}
            columns={columns}
            components={{ Toolbar: GridToolbar }}
            sx={{ width: '100%' }}
          />
        </Box>
      )}

      {/* Dialogue de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer cette collecte ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Annuler
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale d'édition de collecte */}
      <EditCollecteModal
        open={editModalOpen}
        onClose={handleEditClose}
        collecte={collecteToEdit}
        onCollecteUpdated={handleCollecteUpdated}
      />
    </Box>
  );
};

export default DriverCollections;
