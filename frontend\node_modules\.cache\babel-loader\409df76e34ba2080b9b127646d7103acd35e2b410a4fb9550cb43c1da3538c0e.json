{"ast": null, "code": "import axios from 'axios';\nimport { mockVehicles, mockDrivers } from '../data/mockData';\n\n/**\n * Récupère la liste des véhicules depuis l'API\n * @returns {Promise<Array>} Liste des véhicules\n */\nexport const getVehicles = async () => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.get('/api/vehicules', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n\n    // Vérifier si les données sont valides\n    if (response.data && Array.isArray(response.data)) {\n      // S'assurer que chaque véhicule a un ID\n      const validatedData = response.data.map((vehicle, index) => ({\n        ...vehicle,\n        id: vehicle.id || index + 1,\n        immatriculation: vehicle.immatriculation || `VH-${1000 + index}`,\n        statut: vehicle.statut || 'Non spécifié'\n      }));\n      return validatedData;\n    } else {\n      console.warn('Format de données invalide reçu de l\\'API, utilisation des données fictives');\n      return mockVehicles;\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération des véhicules:', error);\n    // Utiliser des données fictives en cas d'erreur\n    return mockVehicles;\n  }\n};\n\n/**\n * Récupère la liste des chauffeurs depuis l'API\n * @returns {Promise<Array>} Liste des chauffeurs\n */\nexport const getDrivers = async () => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.get('/api/users/all', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n\n    // Vérifier si les données sont valides\n    if (response.data && Array.isArray(response.data)) {\n      // Filtrer uniquement les chauffeurs\n      const drivers = response.data.filter(user => user.role === 'chauffeur' || user.role === 'driver' || user.post === 'chauffeur');\n\n      // S'assurer que chaque chauffeur a un ID\n      const validatedDrivers = drivers.map((driver, index) => ({\n        ...driver,\n        id: driver.id || index + 1,\n        nom: driver.nom || driver.name || `Chauffeur ${index + 1}`,\n        email: driver.email || `chauffeur${index + 1}@example.com`\n      }));\n      return validatedDrivers;\n    } else {\n      console.warn('Format de données invalide reçu de l\\'API, utilisation des données fictives');\n      return mockDrivers;\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération des chauffeurs:', error);\n    // Utiliser des données fictives en cas d'erreur\n    return mockDrivers;\n  }\n};\n\n/**\n * Met à jour le statut d'un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {string} status - Nouveau statut\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const updateVehicleStatus = async (vehicleId, status) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`, {\n      statut: status\n    }, {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la mise à jour du statut du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Assigne un chauffeur à un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {number} driverId - ID du chauffeur\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const assignDriverToVehicle = async (vehicleId, driverId) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`, {\n      id_chauffeur: driverId\n    }, {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de l'assignation du chauffeur ${driverId} au véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Met à jour complètement un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {Object} vehicleData - Données du véhicule à mettre à jour\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const updateVehicle = async (vehicleId, vehicleData) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`, vehicleData, {\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la mise à jour du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Crée un nouveau véhicule\n * @param {Object} vehicleData - Données du véhicule à créer\n * @returns {Promise<Object>} Véhicule créé\n */\nexport const createVehicle = async vehicleData => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.post('/api/vehicules', vehicleData, {\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Erreur lors de la création du véhicule:', error);\n    throw error;\n  }\n};\n\n/**\n * Supprime un véhicule\n * @param {number} vehicleId - ID du véhicule à supprimer\n * @returns {Promise<Object>} Résultat de la suppression\n */\nexport const deleteVehicle = async vehicleId => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.delete(`/api/vehicules/${vehicleId}`, {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la suppression du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\nexport default {\n  getVehicles,\n  getDrivers,\n  updateVehicleStatus,\n  assignDriverToVehicle\n};", "map": {"version": 3, "names": ["axios", "mockVehicles", "mockDrivers", "getVehicles", "token", "localStorage", "getItem", "response", "get", "headers", "data", "Array", "isArray", "validatedData", "map", "vehicle", "index", "id", "immatriculation", "statut", "console", "warn", "error", "getDrivers", "drivers", "filter", "user", "role", "post", "validatedDrivers", "driver", "nom", "name", "email", "updateVehicleStatus", "vehicleId", "status", "put", "assignDriverToVehicle", "driverId", "id_chauffeur", "updateVehicle", "vehicleData", "createVehicle", "deleteVehicle", "delete"], "sources": ["C:/Users/<USER>/Desktop/cocal-platforme-test/frontend/src/services/vehicleService.js"], "sourcesContent": ["import axios from 'axios';\nimport { mockVehicles, mockDrivers } from '../data/mockData';\n\n/**\n * Récupère la liste des véhicules depuis l'API\n * @returns {Promise<Array>} Liste des véhicules\n */\nexport const getVehicles = async () => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.get('/api/vehicules', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n\n    // Vérifier si les données sont valides\n    if (response.data && Array.isArray(response.data)) {\n      // S'assurer que chaque véhicule a un ID\n      const validatedData = response.data.map((vehicle, index) => ({\n        ...vehicle,\n        id: vehicle.id || index + 1,\n        immatriculation: vehicle.immatriculation || `VH-${1000 + index}`,\n        statut: vehicle.statut || 'Non spécifié'\n      }));\n      return validatedData;\n    } else {\n      console.warn('Format de données invalide reçu de l\\'API, utilisation des données fictives');\n      return mockVehicles;\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération des véhicules:', error);\n    // Utiliser des données fictives en cas d'erreur\n    return mockVehicles;\n  }\n};\n\n/**\n * Récupère la liste des chauffeurs depuis l'API\n * @returns {Promise<Array>} Liste des chauffeurs\n */\nexport const getDrivers = async () => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.get('/api/users/all', {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n\n    // Vérifier si les données sont valides\n    if (response.data && Array.isArray(response.data)) {\n      // Filtrer uniquement les chauffeurs\n      const drivers = response.data.filter(user =>\n        user.role === 'chauffeur' || user.role === 'driver' || user.post === 'chauffeur'\n      );\n\n      // S'assurer que chaque chauffeur a un ID\n      const validatedDrivers = drivers.map((driver, index) => ({\n        ...driver,\n        id: driver.id || index + 1,\n        nom: driver.nom || driver.name || `Chauffeur ${index + 1}`,\n        email: driver.email || `chauffeur${index + 1}@example.com`\n      }));\n\n      return validatedDrivers;\n    } else {\n      console.warn('Format de données invalide reçu de l\\'API, utilisation des données fictives');\n      return mockDrivers;\n    }\n  } catch (error) {\n    console.error('Erreur lors de la récupération des chauffeurs:', error);\n    // Utiliser des données fictives en cas d'erreur\n    return mockDrivers;\n  }\n};\n\n/**\n * Met à jour le statut d'un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {string} status - Nouveau statut\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const updateVehicleStatus = async (vehicleId, status) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`,\n      { statut: status },\n      {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      }\n    );\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la mise à jour du statut du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Assigne un chauffeur à un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {number} driverId - ID du chauffeur\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const assignDriverToVehicle = async (vehicleId, driverId) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`,\n      { id_chauffeur: driverId },\n      {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      }\n    );\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de l'assignation du chauffeur ${driverId} au véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Met à jour complètement un véhicule\n * @param {number} vehicleId - ID du véhicule\n * @param {Object} vehicleData - Données du véhicule à mettre à jour\n * @returns {Promise<Object>} Véhicule mis à jour\n */\nexport const updateVehicle = async (vehicleId, vehicleData) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.put(`/api/vehicules/${vehicleId}`, vehicleData, {\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la mise à jour du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\n/**\n * Crée un nouveau véhicule\n * @param {Object} vehicleData - Données du véhicule à créer\n * @returns {Promise<Object>} Véhicule créé\n */\nexport const createVehicle = async (vehicleData) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.post('/api/vehicules', vehicleData, {\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error('Erreur lors de la création du véhicule:', error);\n    throw error;\n  }\n};\n\n/**\n * Supprime un véhicule\n * @param {number} vehicleId - ID du véhicule à supprimer\n * @returns {Promise<Object>} Résultat de la suppression\n */\nexport const deleteVehicle = async (vehicleId) => {\n  try {\n    const token = localStorage.getItem('token');\n    const response = await axios.delete(`/api/vehicules/${vehicleId}`, {\n      headers: {\n        'Authorization': `Bearer ${token}`\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(`Erreur lors de la suppression du véhicule ${vehicleId}:`, error);\n    throw error;\n  }\n};\n\nexport default {\n  getVehicles,\n  getDrivers,\n  updateVehicleStatus,\n  assignDriverToVehicle\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,WAAW,QAAQ,kBAAkB;;AAE5D;AACA;AACA;AACA;AACA,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,gBAAgB,EAAE;MACjDC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK;MAClC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIG,QAAQ,CAACG,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAAC,EAAE;MACjD;MACA,MAAMG,aAAa,GAAGN,QAAQ,CAACG,IAAI,CAACI,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,MAAM;QAC3D,GAAGD,OAAO;QACVE,EAAE,EAAEF,OAAO,CAACE,EAAE,IAAID,KAAK,GAAG,CAAC;QAC3BE,eAAe,EAAEH,OAAO,CAACG,eAAe,IAAI,MAAM,IAAI,GAAGF,KAAK,EAAE;QAChEG,MAAM,EAAEJ,OAAO,CAACI,MAAM,IAAI;MAC5B,CAAC,CAAC,CAAC;MACH,OAAON,aAAa;IACtB,CAAC,MAAM;MACLO,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;MAC3F,OAAOpB,YAAY;IACrB;EACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;IACrE;IACA,OAAOrB,YAAY;EACrB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF,MAAMnB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAACQ,GAAG,CAAC,gBAAgB,EAAE;MACjDC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK;MAClC;IACF,CAAC,CAAC;;IAEF;IACA,IAAIG,QAAQ,CAACG,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACG,IAAI,CAAC,EAAE;MACjD;MACA,MAAMc,OAAO,GAAGjB,QAAQ,CAACG,IAAI,CAACe,MAAM,CAACC,IAAI,IACvCA,IAAI,CAACC,IAAI,KAAK,WAAW,IAAID,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACE,IAAI,KAAK,WACvE,CAAC;;MAED;MACA,MAAMC,gBAAgB,GAAGL,OAAO,CAACV,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,MAAM;QACvD,GAAGc,MAAM;QACTb,EAAE,EAAEa,MAAM,CAACb,EAAE,IAAID,KAAK,GAAG,CAAC;QAC1Be,GAAG,EAAED,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,IAAI,IAAI,aAAahB,KAAK,GAAG,CAAC,EAAE;QAC1DiB,KAAK,EAAEH,MAAM,CAACG,KAAK,IAAI,YAAYjB,KAAK,GAAG,CAAC;MAC9C,CAAC,CAAC,CAAC;MAEH,OAAOa,gBAAgB;IACzB,CAAC,MAAM;MACLT,OAAO,CAACC,IAAI,CAAC,6EAA6E,CAAC;MAC3F,OAAOnB,WAAW;IACpB;EACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACtE;IACA,OAAOpB,WAAW;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgC,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;EAC9D,IAAI;IACF,MAAMhC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAACqC,GAAG,CAAC,kBAAkBF,SAAS,EAAE,EAC5D;MAAEhB,MAAM,EAAEiB;IAAO,CAAC,EAClB;MACE3B,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK;MAClC;IACF,CACF,CAAC;IACD,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,uDAAuDa,SAAS,GAAG,EAAEb,KAAK,CAAC;IACzF,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgB,qBAAqB,GAAG,MAAAA,CAAOH,SAAS,EAAEI,QAAQ,KAAK;EAClE,IAAI;IACF,MAAMnC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAACqC,GAAG,CAAC,kBAAkBF,SAAS,EAAE,EAC5D;MAAEK,YAAY,EAAED;IAAS,CAAC,EAC1B;MACE9B,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK;MAClC;IACF,CACF,CAAC;IACD,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,6CAA6CiB,QAAQ,gBAAgBJ,SAAS,GAAG,EAAEb,KAAK,CAAC;IACvG,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,aAAa,GAAG,MAAAA,CAAON,SAAS,EAAEO,WAAW,KAAK;EAC7D,IAAI;IACF,MAAMtC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAACqC,GAAG,CAAC,kBAAkBF,SAAS,EAAE,EAAEO,WAAW,EAAE;MAC3EjC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,6CAA6Ca,SAAS,GAAG,EAAEb,KAAK,CAAC;IAC/E,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,aAAa,GAAG,MAAOD,WAAW,IAAK;EAClD,IAAI;IACF,MAAMtC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAAC4B,IAAI,CAAC,gBAAgB,EAAEc,WAAW,EAAE;MAC/DjC,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,aAAa,GAAG,MAAOT,SAAS,IAAK;EAChD,IAAI;IACF,MAAM/B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,QAAQ,GAAG,MAAMP,KAAK,CAAC6C,MAAM,CAAC,kBAAkBV,SAAS,EAAE,EAAE;MACjE1B,OAAO,EAAE;QACP,eAAe,EAAE,UAAUL,KAAK;MAClC;IACF,CAAC,CAAC;IACF,OAAOG,QAAQ,CAACG,IAAI;EACtB,CAAC,CAAC,OAAOY,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,6CAA6Ca,SAAS,GAAG,EAAEb,KAAK,CAAC;IAC/E,MAAMA,KAAK;EACb;AACF,CAAC;AAED,eAAe;EACbnB,WAAW;EACXoB,UAAU;EACVW,mBAAmB;EACnBI;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}