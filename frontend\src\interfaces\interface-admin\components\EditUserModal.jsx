import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { Formik } from 'formik';
import * as yup from 'yup';
import axios from 'axios';

const EditUserModal = ({ open, onClose, user, onUserUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Schéma de validation
  const userSchema = yup.object().shape({
    nom: yup.string().required('Le nom est requis'),
    prenom: yup.string(),
    email: yup.string().email('Email invalide').required('L\'email est requis'),
    telephone: yup.string(),
    role: yup.string().required('Le rôle est requis'),
    password: yup.string().min(6, 'Le mot de passe doit contenir au moins 6 caractères')
  });

  // Valeurs initiales du formulaire
  const initialValues = {
    nom: user?.nom || '',
    prenom: user?.prenom || '',
    email: user?.email || '',
    telephone: user?.telephone || '',
    role: user?.role || 'commercial',
    password: ''
  };

  const handleFormSubmit = async (values, { setSubmitting }) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      
      // Préparer les données à envoyer (exclure le mot de passe s'il est vide)
      const updateData = { ...values };
      if (!updateData.password) {
        delete updateData.password;
      }

      const response = await axios.put(`/api/users/${user.id}`, updateData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Utilisateur mis à jour avec succès !');
      
      // Notifier le parent que l'utilisateur a été mis à jour
      if (onUserUpdated) {
        onUserUpdated(response.data.user);
      }

      // Fermer la modale après un délai
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 1500);

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      setError(
        error.response?.data?.message || 
        'Erreur lors de la mise à jour de l\'utilisateur'
      );
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  if (!user) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle sx={{ 
        fontSize: '1.5rem', 
        fontWeight: 600, 
        color: '#1976d2',
        borderBottom: '1px solid #e0e0e0',
        pb: 2
      }}>
        Modifier l'utilisateur
      </DialogTitle>

      <Formik
        onSubmit={handleFormSubmit}
        initialValues={initialValues}
        validationSchema={userSchema}
        enableReinitialize={true}
      >
        {({
          values,
          errors,
          touched,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent sx={{ pt: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              <Box display="grid" gap="20px">
                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Nom"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.nom}
                  name="nom"
                  error={!!touched.nom && !!errors.nom}
                  helperText={touched.nom && errors.nom}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Prénom"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.prenom}
                  name="prenom"
                  error={!!touched.prenom && !!errors.prenom}
                  helperText={touched.prenom && errors.prenom}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="email"
                  label="Email"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.email}
                  name="email"
                  error={!!touched.email && !!errors.email}
                  helperText={touched.email && errors.email}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Téléphone"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.telephone}
                  name="telephone"
                  error={!!touched.telephone && !!errors.telephone}
                  helperText={touched.telephone && errors.telephone}
                />

                <FormControl fullWidth>
                  <InputLabel>Rôle</InputLabel>
                  <Select
                    value={values.role}
                    label="Rôle"
                    name="role"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.role && !!errors.role}
                  >
                    <MenuItem value="admin">Administrateur</MenuItem>
                    <MenuItem value="commercial">Commercial</MenuItem>
                    <MenuItem value="chauffeur">Chauffeur</MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  variant="outlined"
                  type="password"
                  label="Nouveau mot de passe (optionnel)"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.password}
                  name="password"
                  error={!!touched.password && !!errors.password}
                  helperText={touched.password && errors.password || "Laissez vide pour conserver le mot de passe actuel"}
                />
              </Box>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button 
                onClick={handleClose}
                variant="outlined"
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading || isSubmitting}
                startIcon={loading ? <CircularProgress size={20} /> : null}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': {
                    backgroundColor: '#1565c0'
                  }
                }}
              >
                {loading ? 'Mise à jour...' : 'Mettre à jour'}
              </Button>
            </DialogActions>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

export default EditUserModal;
