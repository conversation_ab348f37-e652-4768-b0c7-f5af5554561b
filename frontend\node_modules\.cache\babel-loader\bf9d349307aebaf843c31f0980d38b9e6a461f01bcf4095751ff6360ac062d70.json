{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cocal-platforme-test\\\\frontend\\\\src\\\\interfaces\\\\interface-admin\\\\scenes\\\\driver-collections\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport { Box, Typography, useTheme, IconButton, CircularProgress, Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from \"@mui/material\";\nimport { DataGrid, GridToolbar } from \"@mui/x-data-grid\";\nimport { tokens } from \"../../theme\";\nimport Header from \"../../components/Header\";\nimport DeleteOutlineIcon from \"@mui/icons-material/DeleteOutline\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport EditCollecteModal from \"../../components/EditCollecteModal\";\nimport { useState, useEffect } from \"react\";\nimport { deleteCollecte, getAllCollectes, testAuthentication } from \"../../../../services/collecteService\";\nimport { isAuthenticated } from \"../../../../services/authService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DriverCollections = () => {\n  _s();\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n  const [collections, setCollections] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [collectionToDelete, setCollectionToDelete] = useState(null);\n  useEffect(() => {\n    // Vérifier si l'utilisateur est authentifié avant de récupérer les collectes\n    if (isAuthenticated()) {\n      fetchCollections();\n    } else {\n      setError(\"Vous devez être connecté pour accéder à cette page.\");\n      setLoading(false);\n    }\n  }, []);\n  const fetchCollections = async () => {\n    try {\n      console.log(\"Début de la récupération des collectes des chauffeurs\");\n      setLoading(true);\n      setError(null); // Réinitialiser les erreurs précédentes\n\n      // Vérifier si l'utilisateur est authentifié\n      if (!isAuthenticated()) {\n        setError(\"Vous devez être connecté pour accéder à cette page.\");\n        setLoading(false);\n        return;\n      }\n\n      // Récupérer toutes les collectes\n      const allData = await getAllCollectes();\n      console.log(\"Toutes les collectes reçues:\", allData.length);\n\n      // Filtrer les collectes qui ont un id_chauffeur ET un statut \"Terminé\"\n      const driverCollections = allData.filter(item => item.id_chauffeur && (item.statut_collecte === \"Terminé\" || item.statut === \"Terminé\"));\n      console.log(\"Collectes terminées des chauffeurs filtrées:\", driverCollections.length);\n      setCollections(driverCollections);\n      setLoading(false);\n    } catch (err) {\n      console.error(\"Erreur lors de la récupération des collectes des chauffeurs:\", err);\n\n      // Gérer les erreurs d'authentification spécifiquement\n      if (err.message && (err.message.includes('authentifié') || err.message.includes('token') || err.message.includes('401'))) {\n        setError(\"Session expirée ou invalide. Veuillez vous reconnecter.\");\n      } else {\n        setError(\"Erreur lors de la récupération des collectes des chauffeurs. Veuillez réessayer.\");\n      }\n      setLoading(false);\n    }\n  };\n  const handleDeleteClick = collectionId => {\n    setCollectionToDelete(collectionId);\n    setDeleteDialogOpen(true);\n  };\n  const handleDeleteConfirm = async () => {\n    try {\n      await deleteCollecte(collectionToDelete);\n      setCollections(collections.filter(collection => collection.id !== collectionToDelete));\n      setDeleteDialogOpen(false);\n      setCollectionToDelete(null);\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression de la collecte:\", err);\n      setError(\"Erreur lors de la suppression de la collecte. Veuillez réessayer.\");\n      setDeleteDialogOpen(false);\n    }\n  };\n  const handleDeleteCancel = () => {\n    setDeleteDialogOpen(false);\n    setCollectionToDelete(null);\n  };\n  const columns = [{\n    field: \"actions\",\n    headerName: \"Actions\",\n    width: 100,\n    renderCell: params => /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: () => handleDeleteClick(params.row.id),\n      color: \"error\",\n      title: \"Supprimer\",\n      children: /*#__PURE__*/_jsxDEV(DeleteOutlineIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: \"id\",\n    headerName: \"ID\",\n    width: 70\n  }, {\n    field: \"id_point_ramassage\",\n    headerName: \"ID Point\",\n    width: 90\n  }, {\n    field: \"date_ramassage\",\n    headerName: \"Date\",\n    flex: 1,\n    valueGetter: params => {\n      if (!params.row.date_ramassage) return \"\";\n      return new Date(params.row.date_ramassage).toLocaleDateString('fr-FR');\n    }\n  }, {\n    field: \"type_coquillage\",\n    headerName: \"Type de Coquillage\",\n    flex: 1\n  }, {\n    field: \"poids_kg\",\n    headerName: \"Poids (kg)\",\n    flex: 0.7\n  }, {\n    field: \"montant_achat\",\n    headerName: \"Montant (DT)\",\n    flex: 0.7,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Typography, {\n      color: colors.greenAccent[500],\n      children: [params.row.montant_achat, \" DT\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: \"statut_collecte\",\n    headerName: \"Statut\",\n    flex: 0.7,\n    renderCell: params => {\n      const statut = params.row.statut_collecte || \"\";\n      return /*#__PURE__*/_jsxDEV(Box, {\n        width: \"80%\",\n        m: \"0 auto\",\n        p: \"5px\",\n        display: \"flex\",\n        justifyContent: \"center\",\n        backgroundColor: statut === \"Terminé\" ? colors.greenAccent[600] : statut === \"En attente\" ? colors.blueAccent[600] : statut === \"À faire\" ? colors.redAccent[600] : colors.grey[700],\n        borderRadius: \"4px\",\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          color: colors.grey[100],\n          sx: {\n            ml: \"5px\"\n          },\n          children: statut\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: \"id_chauffeur\",\n    headerName: \"ID Chauffeur\",\n    flex: 0.7\n  }, {\n    field: \"nom_chauffeur\",\n    headerName: \"Nom du Chauffeur\",\n    flex: 1,\n    valueGetter: params => {\n      var _params$row$email_cha;\n      return params.row.nom_chauffeur || ((_params$row$email_cha = params.row.email_chauffeur) === null || _params$row$email_cha === void 0 ? void 0 : _params$row$email_cha.split('@')[0]) || \"N/A\";\n    }\n  }, {\n    field: \"created_at\",\n    headerName: \"Créé le\",\n    flex: 1,\n    valueGetter: params => {\n      if (!params.row.created_at) return \"\";\n      return new Date(params.row.created_at).toLocaleDateString('fr-FR');\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    m: \"20px\",\n    width: \"100%\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      title: \"COLLECTES TERMIN\\xC9ES\",\n      subtitle: \"Liste des collectes termin\\xE9es par les chauffeurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"75vh\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"75vh\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        color: \"error\",\n        mb: 2,\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mt: 2,\n        children: error.includes('connect') ? /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: () => window.location.href = '/login',\n          children: \"Se connecter\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: fetchCollections,\n          children: \"R\\xE9essayer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this) : collections.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      flexDirection: \"column\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      height: \"75vh\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        color: colors.grey[500],\n        mb: 2,\n        children: \"Aucune collecte termin\\xE9e trouv\\xE9e\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: colors.grey[400],\n        mb: 3,\n        children: \"Il n'y a pas encore de collectes termin\\xE9es par les chauffeurs ou il y a un probl\\xE8me de connexion.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: fetchCollections,\n        children: \"Actualiser\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      m: \"40px 0 0 0\",\n      height: \"75vh\",\n      sx: {\n        \"& .MuiDataGrid-root\": {\n          border: \"none\"\n        },\n        \"& .MuiDataGrid-cell\": {\n          borderBottom: \"none\"\n        },\n        \"& .name-column--cell\": {\n          color: colors.greenAccent[300]\n        },\n        \"& .MuiDataGrid-columnHeaders\": {\n          backgroundColor: colors.blueAccent[700],\n          borderBottom: \"none\"\n        },\n        \"& .MuiDataGrid-virtualScroller\": {\n          backgroundColor: colors.primary[400]\n        },\n        \"& .MuiDataGrid-footerContainer\": {\n          borderTop: \"none\",\n          backgroundColor: colors.blueAccent[700]\n        },\n        \"& .MuiCheckbox-root\": {\n          color: `${colors.greenAccent[200]} !important`\n        },\n        \"& .MuiDataGrid-toolbarContainer .MuiButton-text\": {\n          color: `${colors.grey[100]} !important`\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(DataGrid, {\n        rows: collections,\n        columns: columns,\n        components: {\n          Toolbar: GridToolbar\n        },\n        sx: {\n          width: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: handleDeleteCancel,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirmer la suppression\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"\\xCAtes-vous s\\xFBr de vouloir supprimer cette collecte ? Cette action est irr\\xE9versible.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCancel,\n          color: \"primary\",\n          children: \"Annuler\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteConfirm,\n          color: \"error\",\n          autoFocus: true,\n          children: \"Supprimer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\n_s(DriverCollections, \"bmPmoE53Sg5nrAbUu72saHJ2f98=\", false, function () {\n  return [useTheme];\n});\n_c = DriverCollections;\nexport default DriverCollections;\nvar _c;\n$RefreshReg$(_c, \"DriverCollections\");", "map": {"version": 3, "names": ["Box", "Typography", "useTheme", "IconButton", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "DataGrid", "GridToolbar", "tokens", "Header", "DeleteOutlineIcon", "EditIcon", "EditCollecteModal", "useState", "useEffect", "deleteCollecte", "getAllCollectes", "testAuthentication", "isAuthenticated", "jsxDEV", "_jsxDEV", "DriverCollections", "_s", "theme", "colors", "palette", "mode", "collections", "setCollections", "loading", "setLoading", "error", "setError", "deleteDialogOpen", "setDeleteDialogOpen", "collectionToDelete", "setCollectionToDelete", "fetchCollections", "console", "log", "allData", "length", "driverCollections", "filter", "item", "id_chauffeur", "statut_collecte", "statut", "err", "message", "includes", "handleDeleteClick", "collectionId", "handleDeleteConfirm", "collection", "id", "handleDeleteCancel", "columns", "field", "headerName", "width", "renderCell", "params", "onClick", "row", "color", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "valueGetter", "date_ramassage", "Date", "toLocaleDateString", "greenAccent", "montant_achat", "m", "p", "display", "justifyContent", "backgroundColor", "blueAccent", "redAccent", "grey", "borderRadius", "sx", "ml", "_params$row$email_cha", "nom_chauffeur", "email_chauffeur", "split", "created_at", "subtitle", "alignItems", "height", "flexDirection", "variant", "mb", "mt", "window", "location", "href", "border", "borderBottom", "primary", "borderTop", "rows", "components", "<PERSON><PERSON><PERSON>", "open", "onClose", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/cocal-platforme-test/frontend/src/interfaces/interface-admin/scenes/driver-collections/index.jsx"], "sourcesContent": ["import { Box, Typography, useTheme, IconButton, CircularProgress, Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from \"@mui/material\";\nimport { DataGrid, GridToolbar } from \"@mui/x-data-grid\";\nimport { tokens } from \"../../theme\";\nimport Header from \"../../components/Header\";\nimport DeleteOutlineIcon from \"@mui/icons-material/DeleteOutline\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport EditCollecteModal from \"../../components/EditCollecteModal\";\nimport { useState, useEffect } from \"react\";\nimport { deleteCollecte, getAllCollectes, testAuthentication } from \"../../../../services/collecteService\";\nimport { isAuthenticated } from \"../../../../services/authService\";\n\nconst DriverCollections = () => {\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n\n  const [collections, setCollections] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [collectionToDelete, setCollectionToDelete] = useState(null);\n\n  useEffect(() => {\n    // Vérifier si l'utilisateur est authentifié avant de récupérer les collectes\n    if (isAuthenticated()) {\n      fetchCollections();\n    } else {\n      setError(\"Vous devez être connecté pour accéder à cette page.\");\n      setLoading(false);\n    }\n  }, []);\n\n  const fetchCollections = async () => {\n    try {\n      console.log(\"Début de la récupération des collectes des chauffeurs\");\n      setLoading(true);\n      setError(null); // Réinitialiser les erreurs précédentes\n\n      // Vérifier si l'utilisateur est authentifié\n      if (!isAuthenticated()) {\n        setError(\"Vous devez être connecté pour accéder à cette page.\");\n        setLoading(false);\n        return;\n      }\n\n      // Récupérer toutes les collectes\n      const allData = await getAllCollectes();\n      console.log(\"Toutes les collectes reçues:\", allData.length);\n\n      // Filtrer les collectes qui ont un id_chauffeur ET un statut \"Terminé\"\n      const driverCollections = allData.filter(item =>\n        item.id_chauffeur &&\n        (item.statut_collecte === \"Terminé\" || item.statut === \"Terminé\")\n      );\n      console.log(\"Collectes terminées des chauffeurs filtrées:\", driverCollections.length);\n\n      setCollections(driverCollections);\n      setLoading(false);\n    } catch (err) {\n      console.error(\"Erreur lors de la récupération des collectes des chauffeurs:\", err);\n\n      // Gérer les erreurs d'authentification spécifiquement\n      if (err.message && (err.message.includes('authentifié') || err.message.includes('token') || err.message.includes('401'))) {\n        setError(\"Session expirée ou invalide. Veuillez vous reconnecter.\");\n      } else {\n        setError(\"Erreur lors de la récupération des collectes des chauffeurs. Veuillez réessayer.\");\n      }\n\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (collectionId) => {\n    setCollectionToDelete(collectionId);\n    setDeleteDialogOpen(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    try {\n      await deleteCollecte(collectionToDelete);\n      setCollections(collections.filter(collection => collection.id !== collectionToDelete));\n      setDeleteDialogOpen(false);\n      setCollectionToDelete(null);\n    } catch (err) {\n      console.error(\"Erreur lors de la suppression de la collecte:\", err);\n      setError(\"Erreur lors de la suppression de la collecte. Veuillez réessayer.\");\n      setDeleteDialogOpen(false);\n    }\n  };\n\n  const handleDeleteCancel = () => {\n    setDeleteDialogOpen(false);\n    setCollectionToDelete(null);\n  };\n\n  const columns = [\n    {\n      field: \"actions\",\n      headerName: \"Actions\",\n      width: 100,\n      renderCell: (params) => (\n        <IconButton\n          onClick={() => handleDeleteClick(params.row.id)}\n          color=\"error\"\n          title=\"Supprimer\"\n        >\n          <DeleteOutlineIcon />\n        </IconButton>\n      ),\n    },\n    { field: \"id\", headerName: \"ID\", width: 70 },\n    {\n      field: \"id_point_ramassage\",\n      headerName: \"ID Point\",\n      width: 90,\n    },\n    {\n      field: \"date_ramassage\",\n      headerName: \"Date\",\n      flex: 1,\n      valueGetter: (params) => {\n        if (!params.row.date_ramassage) return \"\";\n        return new Date(params.row.date_ramassage).toLocaleDateString('fr-FR');\n      }\n    },\n    {\n      field: \"type_coquillage\",\n      headerName: \"Type de Coquillage\",\n      flex: 1,\n    },\n    {\n      field: \"poids_kg\",\n      headerName: \"Poids (kg)\",\n      flex: 0.7,\n    },\n    {\n      field: \"montant_achat\",\n      headerName: \"Montant (DT)\",\n      flex: 0.7,\n      renderCell: (params) => (\n        <Typography color={colors.greenAccent[500]}>\n          {params.row.montant_achat} DT\n        </Typography>\n      ),\n    },\n    {\n      field: \"statut_collecte\",\n      headerName: \"Statut\",\n      flex: 0.7,\n      renderCell: (params) => {\n        const statut = params.row.statut_collecte || \"\";\n        return (\n          <Box\n            width=\"80%\"\n            m=\"0 auto\"\n            p=\"5px\"\n            display=\"flex\"\n            justifyContent=\"center\"\n            backgroundColor={\n              statut === \"Terminé\"\n                ? colors.greenAccent[600]\n                : statut === \"En attente\"\n                ? colors.blueAccent[600]\n                : statut === \"À faire\"\n                ? colors.redAccent[600]\n                : colors.grey[700]\n            }\n            borderRadius=\"4px\"\n          >\n            <Typography color={colors.grey[100]} sx={{ ml: \"5px\" }}>\n              {statut}\n            </Typography>\n          </Box>\n        );\n      },\n    },\n    {\n      field: \"id_chauffeur\",\n      headerName: \"ID Chauffeur\",\n      flex: 0.7,\n    },\n    {\n      field: \"nom_chauffeur\",\n      headerName: \"Nom du Chauffeur\",\n      flex: 1,\n      valueGetter: (params) => {\n        return params.row.nom_chauffeur || params.row.email_chauffeur?.split('@')[0] || \"N/A\";\n      }\n    },\n    {\n      field: \"created_at\",\n      headerName: \"Créé le\",\n      flex: 1,\n      valueGetter: (params) => {\n        if (!params.row.created_at) return \"\";\n        return new Date(params.row.created_at).toLocaleDateString('fr-FR');\n      }\n    },\n  ];\n\n  return (\n    <Box m=\"20px\" width=\"100%\">\n      <Header title=\"COLLECTES TERMINÉES\" subtitle=\"Liste des collectes terminées par les chauffeurs\" />\n\n      {loading ? (\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" height=\"75vh\">\n          <CircularProgress />\n        </Box>\n      ) : error ? (\n        <Box display=\"flex\" flexDirection=\"column\" justifyContent=\"center\" alignItems=\"center\" height=\"75vh\">\n          <Typography variant=\"h5\" color=\"error\" mb={2}>{error}</Typography>\n          <Box mt={2}>\n            {error.includes('connect') ? (\n              <Button\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={() => window.location.href = '/login'}\n              >\n                Se connecter\n              </Button>\n            ) : (\n              <Button\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={fetchCollections}\n              >\n                Réessayer\n              </Button>\n            )}\n          </Box>\n        </Box>\n      ) : collections.length === 0 ? (\n        <Box display=\"flex\" flexDirection=\"column\" justifyContent=\"center\" alignItems=\"center\" height=\"75vh\">\n          <Typography variant=\"h5\" color={colors.grey[500]} mb={2}>\n            Aucune collecte terminée trouvée\n          </Typography>\n          <Typography variant=\"body1\" color={colors.grey[400]} mb={3}>\n            Il n'y a pas encore de collectes terminées par les chauffeurs ou il y a un problème de connexion.\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={fetchCollections}\n          >\n            Actualiser\n          </Button>\n        </Box>\n      ) : (\n        <Box\n          m=\"40px 0 0 0\"\n          height=\"75vh\"\n          sx={{\n            \"& .MuiDataGrid-root\": {\n              border: \"none\",\n            },\n            \"& .MuiDataGrid-cell\": {\n              borderBottom: \"none\",\n            },\n            \"& .name-column--cell\": {\n              color: colors.greenAccent[300],\n            },\n            \"& .MuiDataGrid-columnHeaders\": {\n              backgroundColor: colors.blueAccent[700],\n              borderBottom: \"none\",\n            },\n            \"& .MuiDataGrid-virtualScroller\": {\n              backgroundColor: colors.primary[400],\n            },\n            \"& .MuiDataGrid-footerContainer\": {\n              borderTop: \"none\",\n              backgroundColor: colors.blueAccent[700],\n            },\n            \"& .MuiCheckbox-root\": {\n              color: `${colors.greenAccent[200]} !important`,\n            },\n            \"& .MuiDataGrid-toolbarContainer .MuiButton-text\": {\n              color: `${colors.grey[100]} !important`,\n            },\n          }}\n        >\n          <DataGrid\n            rows={collections}\n            columns={columns}\n            components={{ Toolbar: GridToolbar }}\n            sx={{ width: '100%' }}\n          />\n        </Box>\n      )}\n\n      {/* Dialogue de confirmation de suppression */}\n      <Dialog\n        open={deleteDialogOpen}\n        onClose={handleDeleteCancel}\n      >\n        <DialogTitle>Confirmer la suppression</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Êtes-vous sûr de vouloir supprimer cette collecte ? Cette action est irréversible.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleDeleteCancel} color=\"primary\">\n            Annuler\n          </Button>\n          <Button onClick={handleDeleteConfirm} color=\"error\" autoFocus>\n            Supprimer\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default DriverCollections;\n"], "mappings": ";;AAAA,SAASA,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AACrK,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,cAAc,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,sCAAsC;AAC1G,SAASC,eAAe,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,MAAM2B,MAAM,GAAGhB,MAAM,CAACe,KAAK,CAACE,OAAO,CAACC,IAAI,CAAC;EAEzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAElEC,SAAS,CAAC,MAAM;IACd;IACA,IAAII,eAAe,CAAC,CAAC,EAAE;MACrBmB,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLL,QAAQ,CAAC,qDAAqD,CAAC;MAC/DF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpET,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEhB;MACA,IAAI,CAACd,eAAe,CAAC,CAAC,EAAE;QACtBc,QAAQ,CAAC,qDAAqD,CAAC;QAC/DF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMU,OAAO,GAAG,MAAMxB,eAAe,CAAC,CAAC;MACvCsB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,OAAO,CAACC,MAAM,CAAC;;MAE3D;MACA,MAAMC,iBAAiB,GAAGF,OAAO,CAACG,MAAM,CAACC,IAAI,IAC3CA,IAAI,CAACC,YAAY,KAChBD,IAAI,CAACE,eAAe,KAAK,SAAS,IAAIF,IAAI,CAACG,MAAM,KAAK,SAAS,CAClE,CAAC;MACDT,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEG,iBAAiB,CAACD,MAAM,CAAC;MAErFb,cAAc,CAACc,iBAAiB,CAAC;MACjCZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZV,OAAO,CAACP,KAAK,CAAC,8DAA8D,EAAEiB,GAAG,CAAC;;MAElF;MACA,IAAIA,GAAG,CAACC,OAAO,KAAKD,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAIF,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIF,GAAG,CAACC,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACxHlB,QAAQ,CAAC,yDAAyD,CAAC;MACrE,CAAC,MAAM;QACLA,QAAQ,CAAC,kFAAkF,CAAC;MAC9F;MAEAF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAIC,YAAY,IAAK;IAC1ChB,qBAAqB,CAACgB,YAAY,CAAC;IACnClB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMmB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMtC,cAAc,CAACoB,kBAAkB,CAAC;MACxCP,cAAc,CAACD,WAAW,CAACgB,MAAM,CAACW,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKpB,kBAAkB,CAAC,CAAC;MACtFD,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZV,OAAO,CAACP,KAAK,CAAC,+CAA+C,EAAEiB,GAAG,CAAC;MACnEhB,QAAQ,CAAC,mEAAmE,CAAC;MAC7EE,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMsB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtB,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMqB,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjB1C,OAAA,CAACtB,UAAU;MACTiE,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAACW,MAAM,CAACE,GAAG,CAACT,EAAE,CAAE;MAChDU,KAAK,EAAC,OAAO;MACbC,KAAK,EAAC,WAAW;MAAAC,QAAA,eAEjB/C,OAAA,CAACV,iBAAiB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAEhB,CAAC,EACD;IAAEb,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,EAC5C;IACEF,KAAK,EAAE,oBAAoB;IAC3BC,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,gBAAgB;IACvBC,UAAU,EAAE,MAAM;IAClBa,IAAI,EAAE,CAAC;IACPC,WAAW,EAAGX,MAAM,IAAK;MACvB,IAAI,CAACA,MAAM,CAACE,GAAG,CAACU,cAAc,EAAE,OAAO,EAAE;MACzC,OAAO,IAAIC,IAAI,CAACb,MAAM,CAACE,GAAG,CAACU,cAAc,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;IACxE;EACF,CAAC,EACD;IACElB,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,oBAAoB;IAChCa,IAAI,EAAE;EACR,CAAC,EACD;IACEd,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,YAAY;IACxBa,IAAI,EAAE;EACR,CAAC,EACD;IACEd,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,cAAc;IAC1Ba,IAAI,EAAE,GAAG;IACTX,UAAU,EAAGC,MAAM,iBACjB1C,OAAA,CAACxB,UAAU;MAACqE,KAAK,EAAEzC,MAAM,CAACqD,WAAW,CAAC,GAAG,CAAE;MAAAV,QAAA,GACxCL,MAAM,CAACE,GAAG,CAACc,aAAa,EAAC,KAC5B;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY;EAEhB,CAAC,EACD;IACEb,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,QAAQ;IACpBa,IAAI,EAAE,GAAG;IACTX,UAAU,EAAGC,MAAM,IAAK;MACtB,MAAMf,MAAM,GAAGe,MAAM,CAACE,GAAG,CAAClB,eAAe,IAAI,EAAE;MAC/C,oBACE1B,OAAA,CAACzB,GAAG;QACFiE,KAAK,EAAC,KAAK;QACXmB,CAAC,EAAC,QAAQ;QACVC,CAAC,EAAC,KAAK;QACPC,OAAO,EAAC,MAAM;QACdC,cAAc,EAAC,QAAQ;QACvBC,eAAe,EACbpC,MAAM,KAAK,SAAS,GAChBvB,MAAM,CAACqD,WAAW,CAAC,GAAG,CAAC,GACvB9B,MAAM,KAAK,YAAY,GACvBvB,MAAM,CAAC4D,UAAU,CAAC,GAAG,CAAC,GACtBrC,MAAM,KAAK,SAAS,GACpBvB,MAAM,CAAC6D,SAAS,CAAC,GAAG,CAAC,GACrB7D,MAAM,CAAC8D,IAAI,CAAC,GAAG,CACpB;QACDC,YAAY,EAAC,KAAK;QAAApB,QAAA,eAElB/C,OAAA,CAACxB,UAAU;UAACqE,KAAK,EAAEzC,MAAM,CAAC8D,IAAI,CAAC,GAAG,CAAE;UAACE,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAM,CAAE;UAAAtB,QAAA,EACpDpB;QAAM;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEV;EACF,CAAC,EACD;IACEb,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1Ba,IAAI,EAAE;EACR,CAAC,EACD;IACEd,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,kBAAkB;IAC9Ba,IAAI,EAAE,CAAC;IACPC,WAAW,EAAGX,MAAM,IAAK;MAAA,IAAA4B,qBAAA;MACvB,OAAO5B,MAAM,CAACE,GAAG,CAAC2B,aAAa,MAAAD,qBAAA,GAAI5B,MAAM,CAACE,GAAG,CAAC4B,eAAe,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,KAAK;IACvF;EACF,CAAC,EACD;IACEnC,KAAK,EAAE,YAAY;IACnBC,UAAU,EAAE,SAAS;IACrBa,IAAI,EAAE,CAAC;IACPC,WAAW,EAAGX,MAAM,IAAK;MACvB,IAAI,CAACA,MAAM,CAACE,GAAG,CAAC8B,UAAU,EAAE,OAAO,EAAE;MACrC,OAAO,IAAInB,IAAI,CAACb,MAAM,CAACE,GAAG,CAAC8B,UAAU,CAAC,CAAClB,kBAAkB,CAAC,OAAO,CAAC;IACpE;EACF,CAAC,CACF;EAED,oBACExD,OAAA,CAACzB,GAAG;IAACoF,CAAC,EAAC,MAAM;IAACnB,KAAK,EAAC,MAAM;IAAAO,QAAA,gBACxB/C,OAAA,CAACX,MAAM;MAACyD,KAAK,EAAC,wBAAqB;MAAC6B,QAAQ,EAAC;IAAkD;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEjG1C,OAAO,gBACNT,OAAA,CAACzB,GAAG;MAACsF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACc,UAAU,EAAC,QAAQ;MAACC,MAAM,EAAC,MAAM;MAAA9B,QAAA,eAC3E/C,OAAA,CAACrB,gBAAgB;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,GACJxC,KAAK,gBACPX,OAAA,CAACzB,GAAG;MAACsF,OAAO,EAAC,MAAM;MAACiB,aAAa,EAAC,QAAQ;MAAChB,cAAc,EAAC,QAAQ;MAACc,UAAU,EAAC,QAAQ;MAACC,MAAM,EAAC,MAAM;MAAA9B,QAAA,gBAClG/C,OAAA,CAACxB,UAAU;QAACuG,OAAO,EAAC,IAAI;QAAClC,KAAK,EAAC,OAAO;QAACmC,EAAE,EAAE,CAAE;QAAAjC,QAAA,EAAEpC;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAClEnD,OAAA,CAACzB,GAAG;QAAC0G,EAAE,EAAE,CAAE;QAAAlC,QAAA,EACRpC,KAAK,CAACmB,QAAQ,CAAC,SAAS,CAAC,gBACxB9B,OAAA,CAACpB,MAAM;UACLmG,OAAO,EAAC,WAAW;UACnBlC,KAAK,EAAC,SAAS;UACfF,OAAO,EAAEA,CAAA,KAAMuC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;UAAArC,QAAA,EAChD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETnD,OAAA,CAACpB,MAAM;UACLmG,OAAO,EAAC,WAAW;UACnBlC,KAAK,EAAC,SAAS;UACfF,OAAO,EAAE1B,gBAAiB;UAAA8B,QAAA,EAC3B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ5C,WAAW,CAACc,MAAM,KAAK,CAAC,gBAC1BrB,OAAA,CAACzB,GAAG;MAACsF,OAAO,EAAC,MAAM;MAACiB,aAAa,EAAC,QAAQ;MAAChB,cAAc,EAAC,QAAQ;MAACc,UAAU,EAAC,QAAQ;MAACC,MAAM,EAAC,MAAM;MAAA9B,QAAA,gBAClG/C,OAAA,CAACxB,UAAU;QAACuG,OAAO,EAAC,IAAI;QAAClC,KAAK,EAAEzC,MAAM,CAAC8D,IAAI,CAAC,GAAG,CAAE;QAACc,EAAE,EAAE,CAAE;QAAAjC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnD,OAAA,CAACxB,UAAU;QAACuG,OAAO,EAAC,OAAO;QAAClC,KAAK,EAAEzC,MAAM,CAAC8D,IAAI,CAAC,GAAG,CAAE;QAACc,EAAE,EAAE,CAAE;QAAAjC,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnD,OAAA,CAACpB,MAAM;QACLmG,OAAO,EAAC,WAAW;QACnBlC,KAAK,EAAC,SAAS;QACfF,OAAO,EAAE1B,gBAAiB;QAAA8B,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENnD,OAAA,CAACzB,GAAG;MACFoF,CAAC,EAAC,YAAY;MACdkB,MAAM,EAAC,MAAM;MACbT,EAAE,EAAE;QACF,qBAAqB,EAAE;UACrBiB,MAAM,EAAE;QACV,CAAC;QACD,qBAAqB,EAAE;UACrBC,YAAY,EAAE;QAChB,CAAC;QACD,sBAAsB,EAAE;UACtBzC,KAAK,EAAEzC,MAAM,CAACqD,WAAW,CAAC,GAAG;QAC/B,CAAC;QACD,8BAA8B,EAAE;UAC9BM,eAAe,EAAE3D,MAAM,CAAC4D,UAAU,CAAC,GAAG,CAAC;UACvCsB,YAAY,EAAE;QAChB,CAAC;QACD,gCAAgC,EAAE;UAChCvB,eAAe,EAAE3D,MAAM,CAACmF,OAAO,CAAC,GAAG;QACrC,CAAC;QACD,gCAAgC,EAAE;UAChCC,SAAS,EAAE,MAAM;UACjBzB,eAAe,EAAE3D,MAAM,CAAC4D,UAAU,CAAC,GAAG;QACxC,CAAC;QACD,qBAAqB,EAAE;UACrBnB,KAAK,EAAE,GAAGzC,MAAM,CAACqD,WAAW,CAAC,GAAG,CAAC;QACnC,CAAC;QACD,iDAAiD,EAAE;UACjDZ,KAAK,EAAE,GAAGzC,MAAM,CAAC8D,IAAI,CAAC,GAAG,CAAC;QAC5B;MACF,CAAE;MAAAnB,QAAA,eAEF/C,OAAA,CAACd,QAAQ;QACPuG,IAAI,EAAElF,WAAY;QAClB8B,OAAO,EAAEA,OAAQ;QACjBqD,UAAU,EAAE;UAAEC,OAAO,EAAExG;QAAY,CAAE;QACrCiF,EAAE,EAAE;UAAE5B,KAAK,EAAE;QAAO;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAGDnD,OAAA,CAACnB,MAAM;MACL+G,IAAI,EAAE/E,gBAAiB;MACvBgF,OAAO,EAAEzD,kBAAmB;MAAAW,QAAA,gBAE5B/C,OAAA,CAACf,WAAW;QAAA8D,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACnDnD,OAAA,CAACjB,aAAa;QAAAgE,QAAA,eACZ/C,OAAA,CAAChB,iBAAiB;UAAA+D,QAAA,EAAC;QAEnB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBnD,OAAA,CAAClB,aAAa;QAAAiE,QAAA,gBACZ/C,OAAA,CAACpB,MAAM;UAAC+D,OAAO,EAAEP,kBAAmB;UAACS,KAAK,EAAC,SAAS;UAAAE,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnD,OAAA,CAACpB,MAAM;UAAC+D,OAAO,EAAEV,mBAAoB;UAACY,KAAK,EAAC,OAAO;UAACiD,SAAS;UAAA/C,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjD,EAAA,CA3SID,iBAAiB;EAAA,QACPxB,QAAQ;AAAA;AAAAsH,EAAA,GADlB9F,iBAAiB;AA6SvB,eAAeA,iBAAiB;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}