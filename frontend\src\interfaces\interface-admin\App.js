import { useState, useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import Topbar from "./scenes/global/Topbar";
import Sidebar from "./scenes/global/Sidebar";
import Dashboard from "./scenes/dashboard";
import Team from "./scenes/team";
import Invoices from "./scenes/invoices";
import Contacts from "./scenes/contacts";
import Bar from "./scenes/bar";
import Form from "./scenes/form";
import MontantsParType from "./scenes/line";
import Pie from "./scenes/pie";
import FAQ from "./scenes/faq";
import Geography from "./scenes/geography";
import Vehicles from "./scenes/vehicles";
import Drivers from "./scenes/drivers";
import Commercials from "./scenes/commercials";
import DriverCollections from "./scenes/driver-collections";
import DriverRevenue from "./scenes/driver-revenue";
import Points from "./scenes/points";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { ColorModeContext, useMode } from "./theme";
import Calendar from "./scenes/calendar/calendar";
import "./index.css";
import "./global.css";
import "./preserve-sidebar.css";
import preserveSidebar from "./preserveSidebar";

function App({ onLogout }) {
  console.log("AdminApp - Rendu du composant");

  const [theme, colorMode] = useMode();
  const [isSidebar, setIsSidebar] = useState(true);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Utiliser useEffect pour préserver la barre latérale
  useEffect(() => {
    // Appliquer la fonction preserveSidebar au chargement et à chaque changement de isCollapsed
    preserveSidebar();

    // Exécuter la fonction toutes les 500ms pendant 5 secondes pour s'assurer que la barre latérale reste visible
    const interval = setInterval(preserveSidebar, 500);
    setTimeout(() => clearInterval(interval), 5000);

    // Nettoyer l'intervalle lors du démontage du composant
    return () => clearInterval(interval);
  }, [isCollapsed]);

  // Afficher les informations de l'utilisateur
  const userString = localStorage.getItem('user');
  const token = localStorage.getItem('token');
  console.log("AdminApp - Token:", token ? "Présent" : "Absent");
  console.log("AdminApp - User:", userString || "Absent");

  // Afficher les informations détaillées de l'utilisateur
  try {
    if (userString) {
      const user = JSON.parse(userString);
      console.log("AdminApp - Détails utilisateur:", {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      });
    }
  } catch (error) {
    console.error("AdminApp - Erreur lors du parsing des informations utilisateur:", error);
  }

  return (
    <ColorModeContext.Provider value={colorMode}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div className="app">
          <Sidebar isSidebar={isSidebar} isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
          <main className={`content ${isCollapsed ? 'collapsed' : ''}`}>
            <Topbar setIsSidebar={setIsSidebar} onLogout={onLogout} />
            <div style={{ padding: '20px', width: '98%', margin: '0 auto' }}>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/team" element={<Team />} />
                <Route path="/contacts" element={<Contacts />} />
                <Route path="/invoices" element={<Invoices />} />
                <Route path="/form" element={<Form />} />
                <Route path="/bar" element={<Bar />} />
                <Route path="/pie" element={<Pie />} />
                <Route path="/line" element={<MontantsParType />} />
                <Route path="/faq" element={<FAQ />} />
                <Route path="/calendar" element={<Calendar />} />
                <Route path="/geography" element={<Geography />} />
                <Route path="/vehicles" element={<Vehicles />} />
                <Route path="/drivers" element={<Drivers />} />
                <Route path="/commercials" element={<Commercials />} />
                <Route path="/driver-collections" element={<DriverCollections />} />
                <Route path="/driver-revenue" element={<DriverRevenue />} />
                <Route path="/points" element={<Points />} />
              </Routes>
            </div>
          </main>
        </div>
      </ThemeProvider>
    </ColorModeContext.Provider>
  );
}

export default App;
