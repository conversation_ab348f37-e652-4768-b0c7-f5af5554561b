import axios from 'axios';
import { mockVehicles, mockDrivers } from '../data/mockData';

/**
 * Récupère la liste des véhicules depuis l'API
 * @returns {Promise<Array>} Liste des véhicules
 */
export const getVehicles = async () => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/vehicules', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Vérifier si les données sont valides
    if (response.data && Array.isArray(response.data)) {
      // S'assurer que chaque véhicule a un ID
      const validatedData = response.data.map((vehicle, index) => ({
        ...vehicle,
        id: vehicle.id || index + 1,
        immatriculation: vehicle.immatriculation || `VH-${1000 + index}`,
        statut: vehicle.statut || 'Non spécifié'
      }));
      return validatedData;
    } else {
      console.warn('Format de données invalide reçu de l\'API, utilisation des données fictives');
      return mockVehicles;
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des véhicules:', error);
    // Utiliser des données fictives en cas d'erreur
    return mockVehicles;
  }
};

/**
 * Récupère la liste des chauffeurs depuis l'API
 * @returns {Promise<Array>} Liste des chauffeurs
 */
export const getDrivers = async () => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.get('/api/users/all', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // Vérifier si les données sont valides
    if (response.data && Array.isArray(response.data)) {
      // Filtrer uniquement les chauffeurs
      const drivers = response.data.filter(user =>
        user.role === 'chauffeur' || user.role === 'driver' || user.post === 'chauffeur'
      );

      // S'assurer que chaque chauffeur a un ID
      const validatedDrivers = drivers.map((driver, index) => ({
        ...driver,
        id: driver.id || index + 1,
        nom: driver.nom || driver.name || `Chauffeur ${index + 1}`,
        email: driver.email || `chauffeur${index + 1}@example.com`
      }));

      return validatedDrivers;
    } else {
      console.warn('Format de données invalide reçu de l\'API, utilisation des données fictives');
      return mockDrivers;
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des chauffeurs:', error);
    // Utiliser des données fictives en cas d'erreur
    return mockDrivers;
  }
};

/**
 * Met à jour le statut d'un véhicule
 * @param {number} vehicleId - ID du véhicule
 * @param {string} status - Nouveau statut
 * @returns {Promise<Object>} Véhicule mis à jour
 */
export const updateVehicleStatus = async (vehicleId, status) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.put(`/api/vehicules/${vehicleId}`,
      { statut: status },
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du statut du véhicule ${vehicleId}:`, error);
    throw error;
  }
};

/**
 * Assigne un chauffeur à un véhicule
 * @param {number} vehicleId - ID du véhicule
 * @param {number} driverId - ID du chauffeur
 * @returns {Promise<Object>} Véhicule mis à jour
 */
export const assignDriverToVehicle = async (vehicleId, driverId) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.put(`/api/vehicules/${vehicleId}`,
      { id_chauffeur: driverId },
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de l'assignation du chauffeur ${driverId} au véhicule ${vehicleId}:`, error);
    throw error;
  }
};

/**
 * Met à jour complètement un véhicule
 * @param {number} vehicleId - ID du véhicule
 * @param {Object} vehicleData - Données du véhicule à mettre à jour
 * @returns {Promise<Object>} Véhicule mis à jour
 */
export const updateVehicle = async (vehicleId, vehicleData) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.put(`/api/vehicules/${vehicleId}`, vehicleData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du véhicule ${vehicleId}:`, error);
    throw error;
  }
};

/**
 * Crée un nouveau véhicule
 * @param {Object} vehicleData - Données du véhicule à créer
 * @returns {Promise<Object>} Véhicule créé
 */
export const createVehicle = async (vehicleData) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.post('/api/vehicules', vehicleData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création du véhicule:', error);
    throw error;
  }
};

/**
 * Supprime un véhicule
 * @param {number} vehicleId - ID du véhicule à supprimer
 * @returns {Promise<Object>} Résultat de la suppression
 */
export const deleteVehicle = async (vehicleId) => {
  try {
    const token = localStorage.getItem('token');
    const response = await axios.delete(`/api/vehicules/${vehicleId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la suppression du véhicule ${vehicleId}:`, error);
    throw error;
  }
};

export default {
  getVehicles,
  getDrivers,
  updateVehicleStatus,
  assignDriverToVehicle,
  updateVehicle,
  createVehicle,
  deleteVehicle
};
