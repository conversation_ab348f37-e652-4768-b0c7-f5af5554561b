import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { Formik } from 'formik';
import * as yup from 'yup';
import axios from 'axios';

const EditVehicleModal = ({ open, onClose, vehicle, onVehicleUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [drivers, setDrivers] = useState([]);

  // Charger les chauffeurs
  useEffect(() => {
    const fetchDrivers = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/users/drivers', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        setDrivers(response.data || []);
      } catch (error) {
        console.error('Erreur lors du chargement des chauffeurs:', error);
      }
    };

    if (open) {
      fetchDrivers();
    }
  }, [open]);

  // Schéma de validation
  const vehicleSchema = yup.object().shape({
    immatriculation: yup.string().required('L\'immatriculation est requise'),
    marque: yup.string().required('La marque est requise'),
    modele: yup.string().required('Le modèle est requis'),
    annee: yup.number().min(1900, 'Année invalide').max(new Date().getFullYear() + 1, 'Année invalide'),
    statut: yup.string().required('Le statut est requis'),
    id_chauffeur: yup.number(),
    dernier_entretien: yup.string(),
    prochain_entretien: yup.string()
  });

  // Valeurs initiales du formulaire
  const initialValues = {
    immatriculation: vehicle?.immatriculation || '',
    marque: vehicle?.marque || '',
    modele: vehicle?.modele || '',
    annee: vehicle?.annee || new Date().getFullYear(),
    statut: vehicle?.statut || 'En service',
    id_chauffeur: vehicle?.id_chauffeur || '',
    dernier_entretien: vehicle?.dernier_entretien ? 
      new Date(vehicle.dernier_entretien).toISOString().slice(0, 10) : '',
    prochain_entretien: vehicle?.prochain_entretien ? 
      new Date(vehicle.prochain_entretien).toISOString().slice(0, 10) : ''
  };

  const handleFormSubmit = async (values, { setSubmitting }) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      
      const updateData = {
        ...values,
        annee: parseInt(values.annee),
        id_chauffeur: values.id_chauffeur || null,
        dernier_entretien: values.dernier_entretien || null,
        prochain_entretien: values.prochain_entretien || null
      };

      const response = await axios.put(`/api/vehicules/${vehicle.id}`, updateData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Véhicule mis à jour avec succès !');
      
      // Notifier le parent que le véhicule a été mis à jour
      if (onVehicleUpdated) {
        onVehicleUpdated(response.data.vehicle || response.data);
      }

      // Fermer la modale après un délai
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 1500);

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      setError(
        error.response?.data?.message || 
        'Erreur lors de la mise à jour du véhicule'
      );
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  if (!vehicle) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle sx={{ 
        fontSize: '1.5rem', 
        fontWeight: 600, 
        color: '#1976d2',
        borderBottom: '1px solid #e0e0e0',
        pb: 2
      }}>
        Modifier le véhicule
      </DialogTitle>

      <Formik
        onSubmit={handleFormSubmit}
        initialValues={initialValues}
        validationSchema={vehicleSchema}
        enableReinitialize={true}
      >
        {({
          values,
          errors,
          touched,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent sx={{ pt: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              <Box display="grid" gap="20px">
                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Immatriculation"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.immatriculation}
                  name="immatriculation"
                  error={!!touched.immatriculation && !!errors.immatriculation}
                  helperText={touched.immatriculation && errors.immatriculation}
                />

                <Box display="grid" gridTemplateColumns="1fr 1fr" gap="20px">
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="text"
                    label="Marque"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.marque}
                    name="marque"
                    error={!!touched.marque && !!errors.marque}
                    helperText={touched.marque && errors.marque}
                  />

                  <TextField
                    fullWidth
                    variant="outlined"
                    type="text"
                    label="Modèle"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.modele}
                    name="modele"
                    error={!!touched.modele && !!errors.modele}
                    helperText={touched.modele && errors.modele}
                  />
                </Box>

                <Box display="grid" gridTemplateColumns="1fr 1fr" gap="20px">
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="number"
                    label="Année"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.annee}
                    name="annee"
                    error={!!touched.annee && !!errors.annee}
                    helperText={touched.annee && errors.annee}
                    inputProps={{ min: 1900, max: new Date().getFullYear() + 1 }}
                  />

                  <FormControl fullWidth>
                    <InputLabel>Statut</InputLabel>
                    <Select
                      value={values.statut}
                      label="Statut"
                      name="statut"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!touched.statut && !!errors.statut}
                    >
                      <MenuItem value="En service">En service</MenuItem>
                      <MenuItem value="En maintenance">En maintenance</MenuItem>
                      <MenuItem value="Hors service">Hors service</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <FormControl fullWidth>
                  <InputLabel>Chauffeur assigné</InputLabel>
                  <Select
                    value={values.id_chauffeur}
                    label="Chauffeur assigné"
                    name="id_chauffeur"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.id_chauffeur && !!errors.id_chauffeur}
                  >
                    <MenuItem value="">Non assigné</MenuItem>
                    {drivers.map((driver) => (
                      <MenuItem key={driver.id} value={driver.id}>
                        {driver.nom || driver.name} ({driver.email})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Box display="grid" gridTemplateColumns="1fr 1fr" gap="20px">
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="date"
                    label="Dernier entretien"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.dernier_entretien}
                    name="dernier_entretien"
                    error={!!touched.dernier_entretien && !!errors.dernier_entretien}
                    helperText={touched.dernier_entretien && errors.dernier_entretien}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />

                  <TextField
                    fullWidth
                    variant="outlined"
                    type="date"
                    label="Prochain entretien"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.prochain_entretien}
                    name="prochain_entretien"
                    error={!!touched.prochain_entretien && !!errors.prochain_entretien}
                    helperText={touched.prochain_entretien && errors.prochain_entretien}
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Box>
              </Box>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button 
                onClick={handleClose}
                variant="outlined"
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading || isSubmitting}
                startIcon={loading ? <CircularProgress size={20} /> : null}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': {
                    backgroundColor: '#1565c0'
                  }
                }}
              >
                {loading ? 'Mise à jour...' : 'Mettre à jour'}
              </Button>
            </DialogActions>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

export default EditVehicleModal;
