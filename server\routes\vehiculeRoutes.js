const express = require('express');
const router = express.Router();
const db = require('../config/db');
const { verifyToken } = require('../middleware/auth');

// Récupérer tous les véhicules
router.get('/vehicules', verifyToken, (req, res) => {
  const sql = 'SELECT * FROM vehicule';
  db.query(sql, (err, results) => {
    if (err) {
      console.error('Erreur lors de la récupération des véhicules:', err);
      return res.status(500).json({ error: 'Erreur lors de la récupération des véhicules' });
    }
    res.json(results);
  });
});

// Récupérer le véhicule d'un chauffeur par son ID
router.get('/vehicules/chauffeur/:id', verifyToken, (req, res) => {
  const chauffeurId = req.params.id;
  const sql = 'SELECT * FROM vehicule WHERE id_chauffeur = ?';

  db.query(sql, [chauffeurId], (err, results) => {
    if (err) {
      console.error('Erreur lors de la récupération du véhicule:', err);
      return res.status(500).json({ error: 'Erreur lors de la récupération du véhicule' });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: 'Aucun véhicule trouvé pour ce chauffeur' });
    }

    res.json(results[0]);
  });
});

// Mettre à jour le statut d'un véhicule par ID de chauffeur
router.patch('/vehicules/chauffeur/:id/statut', verifyToken, (req, res) => {
  const chauffeurId = req.params.id;
  const { statut } = req.body;

  if (!statut) {
    return res.status(400).json({ error: 'Le statut est requis' });
  }

  // Mettre à jour le statut du véhicule
  const sql = 'UPDATE vehicule SET statut = ? WHERE id_chauffeur = ?';
  db.query(sql, [statut, chauffeurId], (err, result) => {
    if (err) {
      console.error('Erreur lors de la mise à jour du statut du véhicule:', err);
      return res.status(500).json({ error: 'Erreur lors de la mise à jour du statut du véhicule' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Aucun véhicule trouvé pour ce chauffeur' });
    }

    // Si le statut est "En maintenance" ou "Hors service", créer une notification
    if (statut === 'En maintenance' || statut === 'Hors service') {
      // Récupérer les informations du véhicule
      db.query('SELECT * FROM vehicule WHERE id_chauffeur = ?', [chauffeurId], (vehicleErr, vehicleResults) => {
        if (!vehicleErr && vehicleResults.length > 0) {
          const vehicleInfo = vehicleResults[0];

          // Créer une notification
          const notification = {
            type: 'vehicule_statut',
            message: `Véhicule ${vehicleInfo.immatriculation} du chauffeur ID ${chauffeurId} est maintenant ${statut}`,
            details: JSON.stringify({
              immatriculation: vehicleInfo.immatriculation,
              chauffeurId: chauffeurId,
              statut: statut
            }),
            destinataire: 'admin',
            status: 'non_lu'
          };

          // Insérer la notification dans la base de données
          db.query('INSERT INTO notifications SET ?', notification, (notifErr, notifResult) => {
            if (notifErr) {
              console.error('Erreur lors de la création de la notification:', notifErr);
            } else {
              console.log('Notification créée avec succès:', { id: notifResult.insertId, ...notification });
            }
          });
        }
      });
    }

    res.json({ id_chauffeur: chauffeurId, statut });
  });
});

// Mettre à jour un véhicule complet
router.put('/vehicules/:id', verifyToken, (req, res) => {
  const vehicleId = req.params.id;
  const { immatriculation, marque, modele, annee, statut, id_chauffeur, dernier_entretien, prochain_entretien } = req.body;

  // Construire la requête SQL dynamiquement
  const updateFields = [];
  const updateValues = [];

  if (immatriculation !== undefined) {
    updateFields.push('immatriculation = ?');
    updateValues.push(immatriculation);
  }
  if (marque !== undefined) {
    updateFields.push('marque = ?');
    updateValues.push(marque);
  }
  if (modele !== undefined) {
    updateFields.push('modele = ?');
    updateValues.push(modele);
  }
  if (annee !== undefined) {
    updateFields.push('annee = ?');
    updateValues.push(annee);
  }
  if (statut !== undefined) {
    updateFields.push('statut = ?');
    updateValues.push(statut);
  }
  if (id_chauffeur !== undefined) {
    updateFields.push('id_chauffeur = ?');
    updateValues.push(id_chauffeur);
  }
  if (dernier_entretien !== undefined) {
    updateFields.push('dernier_entretien = ?');
    updateValues.push(dernier_entretien);
  }
  if (prochain_entretien !== undefined) {
    updateFields.push('prochain_entretien = ?');
    updateValues.push(prochain_entretien);
  }

  if (updateFields.length === 0) {
    return res.status(400).json({ error: 'Aucune donnée à mettre à jour' });
  }

  updateValues.push(vehicleId);
  const sql = `UPDATE vehicule SET ${updateFields.join(', ')} WHERE id = ?`;

  db.query(sql, updateValues, (err, result) => {
    if (err) {
      console.error('Erreur lors de la mise à jour du véhicule:', err);
      return res.status(500).json({ error: 'Erreur lors de la mise à jour du véhicule' });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Véhicule non trouvé' });
    }

    // Si le statut est "En maintenance" ou "Hors service", créer une notification
    if (statut && (statut === 'En maintenance' || statut === 'Hors service')) {
      // Récupérer les informations du véhicule
      db.query('SELECT * FROM vehicule WHERE id = ?', [vehicleId], (vehicleErr, vehicleResults) => {
        if (!vehicleErr && vehicleResults.length > 0) {
          const vehicleInfo = vehicleResults[0];

          // Créer une notification
          const notification = {
            type: 'vehicule_statut',
            message: `Véhicule ${vehicleInfo.immatriculation} (ID: ${vehicleId}) est maintenant ${statut}`,
            details: JSON.stringify({
              vehiculeId: vehicleId,
              immatriculation: vehicleInfo.immatriculation,
              statut: statut
            }),
            destinataire: 'admin',
            status: 'non_lu'
          };

          db.query('INSERT INTO notifications SET ?', notification, (notifErr, notifResult) => {
            if (notifErr) {
              console.error('Erreur lors de la création de la notification:', notifErr);
            } else {
              console.log('Notification créée avec succès:', { id: notifResult.insertId, ...notification });
            }
          });
        }
      });
    }

    res.json({
      message: 'Véhicule mis à jour avec succès',
      vehicle: { id: vehicleId, ...req.body }
    });
  });
});

// Supprimer un véhicule
router.delete('/vehicules/:id', verifyToken, (req, res) => {
  const sql = 'DELETE FROM vehicule WHERE id = ?';
  db.query(sql, [req.params.id], (err, result) => {
    if (err) {
      console.error('Erreur lors de la suppression du véhicule:', err);
      return res.status(500).json({ error: 'Erreur lors de la suppression du véhicule' });
    }
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Véhicule non trouvé' });
    }
    res.json({ message: 'Véhicule supprimé avec succès' });
  });
});

module.exports = router;
