import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { Formik } from 'formik';
import * as yup from 'yup';
import axios from 'axios';

const EditPointModal = ({ open, onClose, point, onPointUpdated }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Schéma de validation
  const pointSchema = yup.object().shape({
    nom_restaurant_cafe: yup.string().required('Le nom du restaurant/café est requis'),
    adresse: yup.string().required('L\'adresse est requise'),
    telephone: yup.string(),
    type_coquillage: yup.string(),
    latitude: yup.number(),
    longitude: yup.number()
  });

  // Valeurs initiales du formulaire
  const initialValues = {
    nom_restaurant_cafe: point?.nom_restaurant_cafe || point?.nom || '',
    adresse: point?.adresse || '',
    telephone: point?.telephone || '',
    type_coquillage: point?.type_coquillage || 'Huîtres',
    latitude: point?.latitude || '',
    longitude: point?.longitude || ''
  };

  const handleFormSubmit = async (values, { setSubmitting }) => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      
      const updateData = {
        ...values,
        latitude: values.latitude ? parseFloat(values.latitude) : null,
        longitude: values.longitude ? parseFloat(values.longitude) : null
      };

      const response = await axios.put(`/api/points/${point.id}`, updateData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Point de ramassage mis à jour avec succès !');
      
      // Notifier le parent que le point a été mis à jour
      if (onPointUpdated) {
        onPointUpdated(response.data.point || response.data);
      }

      // Fermer la modale après un délai
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 1500);

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      setError(
        error.response?.data?.message || 
        'Erreur lors de la mise à jour du point de ramassage'
      );
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    setError('');
    setSuccess('');
    onClose();
  };

  if (!point) return null;

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle sx={{ 
        fontSize: '1.5rem', 
        fontWeight: 600, 
        color: '#1976d2',
        borderBottom: '1px solid #e0e0e0',
        pb: 2
      }}>
        Modifier le point de ramassage
      </DialogTitle>

      <Formik
        onSubmit={handleFormSubmit}
        initialValues={initialValues}
        validationSchema={pointSchema}
        enableReinitialize={true}
      >
        {({
          values,
          errors,
          touched,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent sx={{ pt: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}

              <Box display="grid" gap="20px">
                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Nom du restaurant/café"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.nom_restaurant_cafe}
                  name="nom_restaurant_cafe"
                  error={!!touched.nom_restaurant_cafe && !!errors.nom_restaurant_cafe}
                  helperText={touched.nom_restaurant_cafe && errors.nom_restaurant_cafe}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Adresse"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.adresse}
                  name="adresse"
                  error={!!touched.adresse && !!errors.adresse}
                  helperText={touched.adresse && errors.adresse}
                  multiline
                  rows={2}
                />

                <TextField
                  fullWidth
                  variant="outlined"
                  type="text"
                  label="Téléphone"
                  onBlur={handleBlur}
                  onChange={handleChange}
                  value={values.telephone}
                  name="telephone"
                  error={!!touched.telephone && !!errors.telephone}
                  helperText={touched.telephone && errors.telephone}
                />

                <FormControl fullWidth>
                  <InputLabel>Type de coquillage principal</InputLabel>
                  <Select
                    value={values.type_coquillage}
                    label="Type de coquillage principal"
                    name="type_coquillage"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={!!touched.type_coquillage && !!errors.type_coquillage}
                  >
                    <MenuItem value="Huîtres">Huîtres</MenuItem>
                    <MenuItem value="Moules">Moules</MenuItem>
                    <MenuItem value="Palourdes">Palourdes</MenuItem>
                    <MenuItem value="Coques">Coques</MenuItem>
                    <MenuItem value="Autres">Autres</MenuItem>
                  </Select>
                </FormControl>

                <Box display="grid" gridTemplateColumns="1fr 1fr" gap="20px">
                  <TextField
                    fullWidth
                    variant="outlined"
                    type="number"
                    label="Latitude"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.latitude}
                    name="latitude"
                    error={!!touched.latitude && !!errors.latitude}
                    helperText={touched.latitude && errors.latitude}
                    inputProps={{ step: "any" }}
                  />

                  <TextField
                    fullWidth
                    variant="outlined"
                    type="number"
                    label="Longitude"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.longitude}
                    name="longitude"
                    error={!!touched.longitude && !!errors.longitude}
                    helperText={touched.longitude && errors.longitude}
                    inputProps={{ step: "any" }}
                  />
                </Box>
              </Box>
            </DialogContent>

            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button 
                onClick={handleClose}
                variant="outlined"
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={loading || isSubmitting}
                startIcon={loading ? <CircularProgress size={20} /> : null}
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': {
                    backgroundColor: '#1565c0'
                  }
                }}
              >
                {loading ? 'Mise à jour...' : 'Mettre à jour'}
              </Button>
            </DialogActions>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

export default EditPointModal;
