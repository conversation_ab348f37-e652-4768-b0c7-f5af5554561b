import { Box, Typography, useTheme, CircularProgress, Button, IconButton } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { tokens } from "../../theme";
import Header from "../../components/Header";
import EditIcon from "@mui/icons-material/Edit";
import EditVehicleModal from "../../components/EditVehicleModal";
import { useState, useEffect } from "react";
import { getVehicles, getDrivers } from "../../../../services/vehicleService";

const Vehicles = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const [vehicles, setVehicles] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [vehicleToEdit, setVehicleToEdit] = useState(null);

  // Fonction pour récupérer les véhicules et les chauffeurs
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Récupérer les véhicules et les chauffeurs en parallèle
        const [vehiclesData, driversData] = await Promise.all([
          getVehicles(),
          getDrivers()
        ]);

        // Créer un dictionnaire des chauffeurs par ID
        const driversMap = {};
        driversData.forEach(driver => {
          driversMap[driver.id] = driver;
        });

        // Formater les données des véhicules
        const formattedVehicles = vehiclesData.map((vehicle, index) => {
          const driver = driversMap[vehicle.id_chauffeur];
          return {
            // Assurer que chaque ligne a un ID unique
            id: vehicle.id || index + 1,
            registration: vehicle.immatriculation || `VH-${1000 + index}`,
            model: vehicle.marque && vehicle.modele ? `${vehicle.marque} ${vehicle.modele}` : 'Non spécifié',
            year: vehicle.dernier_entretien ? new Date(vehicle.dernier_entretien).getFullYear() : new Date().getFullYear(),
            status: vehicle.statut || 'Non spécifié',
            driverId: vehicle.id_chauffeur || 0,
            driver: driver ? (driver.name || driver.nom || driver.email) : 'Non assigné',
            lastMaintenance: vehicle.dernier_entretien ? new Date(vehicle.dernier_entretien).toLocaleDateString('fr-FR') : 'Non défini',
            nextMaintenance: vehicle.prochain_entretien ? new Date(vehicle.prochain_entretien).toLocaleDateString('fr-FR') : 'Non défini'
          };
        });

        setVehicles(formattedVehicles);
        setDrivers(driversData);
        setLoading(false);
      } catch (err) {
        console.error('Erreur lors de la récupération des données:', err);
        setError('Erreur lors de la récupération des données. Veuillez réessayer.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleEditClick = (vehicle) => {
    setVehicleToEdit(vehicle);
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setVehicleToEdit(null);
  };

  const handleVehicleUpdated = (updatedVehicle) => {
    setVehicles(vehicles.map(vehicle =>
      vehicle.id === updatedVehicle.id ? { ...vehicle, ...updatedVehicle } : vehicle
    ));
  };

  const columns = [
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      renderCell: (params) => (
        <IconButton
          onClick={() => handleEditClick(params.row)}
          color="primary"
          title="Modifier"
          size="small"
        >
          <EditIcon />
        </IconButton>
      ),
    },
    { field: "id", headerName: "ID", flex: 0.5 },
    { field: "registration", headerName: "Immatriculation", flex: 1 },
    { field: "model", headerName: "Modèle", flex: 1 },
    { field: "year", headerName: "Année", flex: 0.5 },
    {
      field: "status",
      headerName: "Statut",
      flex: 1,
      renderCell: ({ row: { status } }) => {
        return (
          <Box
            width="60%"
            m="0 auto"
            p="5px"
            display="flex"
            justifyContent="center"
            backgroundColor={
              status === "En service" || status === "en service"
                ? colors.greenAccent[600]
                : status === "En maintenance" || status === "en maintenance"
                ? colors.blueAccent[700]
                : colors.redAccent[700]
            }
            borderRadius="4px"
          >
            <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
              {status}
            </Typography>
          </Box>
        );
      },
    },
    { field: "driverId", headerName: "ID Chauffeur", flex: 0.7 },
    { field: "driver", headerName: "Chauffeur", flex: 1 },
    { field: "lastMaintenance", headerName: "Dernière maintenance", flex: 1 },
    { field: "nextMaintenance", headerName: "Prochaine maintenance", flex: 1 },
  ];

  return (
    <Box m="20px" width="100%">
      <Header title="VÉHICULES" subtitle="Gestion des véhicules" />

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <Typography variant="h5" color="error">{error}</Typography>
        </Box>
      ) : (
        <Box
          m="40px 0 0 0"
          height="75vh"
          sx={{
            "& .MuiDataGrid-root": {
              border: "none",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "none",
            },
            "& .name-column--cell": {
              color: colors.greenAccent[300],
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.blueAccent[700],
              borderBottom: "none",
            },
            "& .MuiDataGrid-virtualScroller": {
              backgroundColor: colors.primary[400],
            },
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              backgroundColor: colors.blueAccent[700],
            },
            "& .MuiCheckbox-root": {
              color: `${colors.greenAccent[200]} !important`,
            },
          }}
        >
          <DataGrid
            rows={vehicles}
            columns={columns}
            sx={{ width: '100%' }}
            getRowId={(row) => row.id}
            initialState={{
              sorting: {
                sortModel: [{ field: 'id', sort: 'asc' }],
              },
            }}
          />
        </Box>
      )}

      {/* Modale d'édition de véhicule */}
      <EditVehicleModal
        open={editModalOpen}
        onClose={handleEditClose}
        vehicle={vehicleToEdit}
        onVehicleUpdated={handleVehicleUpdated}
      />
    </Box>
  );
};

export default Vehicles;
