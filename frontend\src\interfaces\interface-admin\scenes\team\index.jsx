import { Box, Typography, useTheme, IconButton, CircularProgress, <PERSON>ton, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from "@mui/material";
import { DataGrid, GridToolbar } from "@mui/x-data-grid";
import { tokens } from "../../theme";
import AdminPanelSettingsOutlinedIcon from "@mui/icons-material/AdminPanelSettingsOutlined";
import LockOpenOutlinedIcon from "@mui/icons-material/LockOpenOutlined";
import SecurityOutlinedIcon from "@mui/icons-material/SecurityOutlined";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import EditIcon from "@mui/icons-material/Edit";
import Header from "../../components/Header";
import EditUserModal from "../../components/EditUserModal";
import { useState, useEffect } from "react";
import { getAllUsers, deleteUser } from "../../../../services/userService";

const Team = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const data = await getAllUsers();
      setUsers(data);
      setLoading(false);
    } catch (err) {
      console.error("Erreur lors de la récupération des utilisateurs:", err);
      setError("Erreur lors de la récupération des utilisateurs. Veuillez réessayer.");
      setLoading(false);
    }
  };

  const handleDeleteClick = (userId) => {
    setUserToDelete(userId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteUser(userToDelete);
      setUsers(users.filter(user => user.id !== userToDelete));
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    } catch (err) {
      console.error("Erreur lors de la suppression de l'utilisateur:", err);
      setError("Erreur lors de la suppression de l'utilisateur. Veuillez réessayer.");
      setDeleteDialogOpen(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setUserToDelete(null);
  };

  const handleEditClick = (user) => {
    setUserToEdit(user);
    setEditModalOpen(true);
  };

  const handleEditClose = () => {
    setEditModalOpen(false);
    setUserToEdit(null);
  };

  const handleUserUpdated = (updatedUser) => {
    setUsers(users.map(user =>
      user.id === updatedUser.id ? { ...user, ...updatedUser } : user
    ));
  };

  const columns = [
    {
      field: "actions",
      headerName: "Actions",
      width: 150,
      renderCell: (params) => (
        <Box display="flex" gap="8px">
          <IconButton
            onClick={() => handleEditClick(params.row)}
            color="primary"
            title="Modifier"
            size="small"
          >
            <EditIcon />
          </IconButton>
          <IconButton
            onClick={() => handleDeleteClick(params.row.id)}
            color="error"
            title="Supprimer"
            size="small"
          >
            <DeleteOutlineIcon />
          </IconButton>
        </Box>
      ),
    },
    { field: "id", headerName: "ID", width: 70 },
    {
      field: "name",
      headerName: "Nom",
      flex: 1,
      cellClassName: "name-column--cell",
      valueGetter: (params) => params.row.name || params.row.email.split('@')[0]
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
    },
    {
      field: "role",
      headerName: "Rôle",
      flex: 1,
      renderCell: ({ row }) => {
        const role = row.role || "user";
        return (
          <Box
            width="60%"
            m="0 auto"
            p="5px"
            display="flex"
            justifyContent="center"
            backgroundColor={
              role === "admin"
                ? colors.greenAccent[600]
                : role === "commercial"
                ? colors.blueAccent[600]
                : role === "chauffeur"
                ? colors.redAccent[600]
                : colors.greenAccent[700]
            }
            borderRadius="4px"
          >
            {role === "admin" && <AdminPanelSettingsOutlinedIcon />}
            {role === "commercial" && <SecurityOutlinedIcon />}
            {role === "chauffeur" && <LockOpenOutlinedIcon />}
            <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
              {role === "admin" ? "Admin" :
               role === "commercial" ? "Commercial" :
               role === "chauffeur" ? "Chauffeur" : "Utilisateur"}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "created_at",
      headerName: "Date de création",
      flex: 1,
      valueGetter: (params) => {
        if (!params.row.created_at) return "";
        return new Date(params.row.created_at).toLocaleDateString('fr-FR');
      }
    },
  ];

  return (
    <Box m="20px" width="100%">
      <Header title="ÉQUIPE" subtitle="Gestion des membres de l'équipe" />

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box display="flex" justifyContent="center" alignItems="center" height="75vh">
          <Typography variant="h5" color="error">{error}</Typography>
        </Box>
      ) : (
        <Box
          m="40px 0 0 0"
          height="75vh"
          sx={{
            "& .MuiDataGrid-root": {
              border: "none",
            },
            "& .MuiDataGrid-cell": {
              borderBottom: "none",
            },
            "& .name-column--cell": {
              color: colors.greenAccent[300],
            },
            "& .MuiDataGrid-columnHeaders": {
              backgroundColor: colors.blueAccent[700],
              borderBottom: "none",
            },
            "& .MuiDataGrid-virtualScroller": {
              backgroundColor: colors.primary[400],
            },
            "& .MuiDataGrid-footerContainer": {
              borderTop: "none",
              backgroundColor: colors.blueAccent[700],
            },
            "& .MuiCheckbox-root": {
              color: `${colors.greenAccent[200]} !important`,
            },
            "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
              color: `${colors.grey[100]} !important`,
            },
          }}
        >
          <DataGrid
            rows={users}
            columns={columns}
            components={{ Toolbar: GridToolbar }}
            sx={{ width: '100%' }}
          />
        </Box>
      )}

      {/* Dialogue de confirmation de suppression */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Êtes-vous sûr de vouloir supprimer cet utilisateur ? Cette action est irréversible.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Annuler
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modale d'édition d'utilisateur */}
      <EditUserModal
        open={editModalOpen}
        onClose={handleEditClose}
        user={userToEdit}
        onUserUpdated={handleUserUpdated}
      />
    </Box>
  );
};

export default Team;
