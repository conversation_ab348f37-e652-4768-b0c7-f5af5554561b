{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cocal-platforme-test\\\\frontend\\\\src\\\\interfaces\\\\interface-admin\\\\scenes\\\\global\\\\Sidebar.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { ProSidebar, Menu, MenuItem } from \"react-pro-sidebar\";\nimport { Box, IconButton, Typography, useTheme } from \"@mui/material\";\nimport { Link } from \"react-router-dom\";\nimport \"react-pro-sidebar/dist/css/styles.css\";\nimport \"./Sidebar.css\";\nimport UserAvatar from \"../../../../components/common/UserAvatar\";\nimport { tokens } from \"../../theme\";\nimport HomeOutlinedIcon from \"@mui/icons-material/HomeOutlined\";\nimport PeopleOutlinedIcon from \"@mui/icons-material/PeopleOutlined\";\nimport ContactsOutlinedIcon from \"@mui/icons-material/ContactsOutlined\";\nimport ReceiptOutlinedIcon from \"@mui/icons-material/ReceiptOutlined\";\nimport PersonOutlinedIcon from \"@mui/icons-material/PersonOutlined\";\nimport TrafficIcon from \"@mui/icons-material/Traffic\";\nimport LocalShippingIcon from \"@mui/icons-material/LocalShipping\";\nimport MonetizationOnIcon from \"@mui/icons-material/MonetizationOn\";\nimport BarChartOutlinedIcon from \"@mui/icons-material/BarChartOutlined\";\nimport PieChartOutlineOutlinedIcon from \"@mui/icons-material/PieChartOutlineOutlined\";\nimport TimelineOutlinedIcon from \"@mui/icons-material/TimelineOutlined\";\nimport MenuOutlinedIcon from \"@mui/icons-material/MenuOutlined\";\nimport MapOutlinedIcon from \"@mui/icons-material/MapOutlined\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Item = ({\n  title,\n  to,\n  icon,\n  selected,\n  setSelected\n}) => {\n  _s();\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n  // Préfixer les routes avec /admin\n  const fullPath = to.startsWith('/') ? `/admin${to}` : `/admin/${to}`;\n  return /*#__PURE__*/_jsxDEV(MenuItem, {\n    active: selected === title,\n    style: {\n      color: colors.grey[100]\n    },\n    onClick: () => setSelected(title),\n    icon: icon,\n    children: /*#__PURE__*/_jsxDEV(Link, {\n      to: fullPath,\n      style: {\n        textDecoration: 'none',\n        color: 'inherit',\n        display: 'flex',\n        alignItems: 'center',\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(Item, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = Item;\nconst Sidebar = ({\n  isSidebar,\n  isCollapsed,\n  setIsCollapsed\n}) => {\n  _s2();\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n  const [selected, setSelected] = useState(\"Dashboard\");\n  const [user, setUser] = useState({\n    name: \"Utilisateur\",\n    role: \"admin\"\n  });\n  useEffect(() => {\n    // Récupérer les informations de l'utilisateur depuis le localStorage\n    const userString = localStorage.getItem('user');\n    if (userString) {\n      try {\n        const userData = JSON.parse(userString);\n        setUser(userData);\n      } catch (error) {\n        console.error(\"Erreur lors de la récupération des informations utilisateur:\", error);\n      }\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      \"& .pro-sidebar-inner\": {\n        background: `${colors.primary[400]} !important`\n      },\n      \"& .pro-icon-wrapper\": {\n        backgroundColor: \"transparent !important\"\n      },\n      \"& .pro-inner-item\": {\n        padding: \"5px 35px 5px 20px !important\"\n      },\n      \"& .pro-inner-item:hover\": {\n        color: \"#38bdf8 !important\"\n      },\n      \"& .pro-menu-item.active\": {\n        color: \"#0ea5e9 !important\"\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(ProSidebar, {\n      collapsed: isCollapsed,\n      style: {\n        height: '100vh',\n        position: 'fixed',\n        left: 0,\n        top: 0,\n        zIndex: 1000,\n        width: isCollapsed ? '80px' : '250px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Menu, {\n        iconShape: \"square\",\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          onClick: () => setIsCollapsed(!isCollapsed),\n          icon: isCollapsed ? /*#__PURE__*/_jsxDEV(MenuOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 33\n          }, this) : undefined,\n          style: {\n            margin: \"10px 0 20px 0\",\n            color: colors.grey[100]\n          },\n          children: !isCollapsed && /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            ml: \"15px\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              color: colors.grey[100],\n              children: \"ADMINIS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setIsCollapsed(!isCollapsed),\n              children: /*#__PURE__*/_jsxDEV(MenuOutlinedIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(Box, {\n          mb: \"25px\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/_jsxDEV(UserAvatar, {\n              name: user.name,\n              size: 100,\n              sx: {\n                cursor: \"pointer\",\n                border: `2px solid ${colors.blueAccent[500]}`,\n                boxShadow: `0 4px 12px rgba(56, 189, 248, 0.2)`\n              },\n              tooltipTitle: `${user.name} - ${user.role === 'admin' ? 'Administrateur' : user.role === 'commercial' ? 'Commercial' : user.role === 'driver' ? 'Chauffeur' : 'Utilisateur'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              color: colors.grey[100],\n              fontWeight: \"bold\",\n              sx: {\n                m: \"10px 0 0 0\"\n              },\n              children: user.name || 'Utilisateur'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              color: colors.blueAccent[500],\n              children: user.role === 'admin' ? 'Administrateur' : user.role === 'commercial' ? 'Commercial' : user.role === 'driver' ? 'Chauffeur' : 'Utilisateur'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          paddingLeft: isCollapsed ? undefined : \"10%\",\n          children: [/*#__PURE__*/_jsxDEV(Item, {\n            title: \"Dashboard\",\n            to: \"/\",\n            icon: /*#__PURE__*/_jsxDEV(HomeOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: colors.grey[500],\n            sx: {\n              m: \"15px 0 5px 20px\"\n            },\n            children: \"Data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Manage Team\",\n            to: \"/team\",\n            icon: /*#__PURE__*/_jsxDEV(PeopleOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Contacts Information\",\n            to: \"/contacts\",\n            icon: /*#__PURE__*/_jsxDEV(ContactsOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Invoices Balances\",\n            to: \"/invoices\",\n            icon: /*#__PURE__*/_jsxDEV(ReceiptOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: colors.grey[500],\n            sx: {\n              m: \"15px 0 5px 20px\"\n            },\n            children: \"Pages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Profile Form\",\n            to: \"/form\",\n            icon: /*#__PURE__*/_jsxDEV(PersonOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: colors.grey[500],\n            sx: {\n              m: \"15px 0 5px 20px\"\n            },\n            children: \"Charts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Bar Chart\",\n            to: \"/bar\",\n            icon: /*#__PURE__*/_jsxDEV(BarChartOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Pie Chart\",\n            to: \"/pie\",\n            icon: /*#__PURE__*/_jsxDEV(PieChartOutlineOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Montants par Type\",\n            to: \"/line\",\n            icon: /*#__PURE__*/_jsxDEV(TimelineOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Geography Chart\",\n            to: \"/geography\",\n            icon: /*#__PURE__*/_jsxDEV(MapOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"G\\xE9rer les v\\xE9hicules\",\n            to: \"/vehicles\",\n            icon: /*#__PURE__*/_jsxDEV(TrafficIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"G\\xE9rer les chauffeurs\",\n            to: \"/drivers\",\n            icon: /*#__PURE__*/_jsxDEV(PeopleOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"G\\xE9rer les commerciaux\",\n            to: \"/commercials\",\n            icon: /*#__PURE__*/_jsxDEV(PeopleOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Collectes termin\\xE9es\",\n            to: \"/driver-collections\",\n            icon: /*#__PURE__*/_jsxDEV(LocalShippingIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Montants par chauffeur\",\n            to: \"/driver-revenue\",\n            icon: /*#__PURE__*/_jsxDEV(MonetizationOnIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Item, {\n            title: \"Points de ramassage\",\n            to: \"/points\",\n            icon: /*#__PURE__*/_jsxDEV(MapOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 21\n            }, this),\n            selected: selected,\n            setSelected: setSelected\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s2(Sidebar, \"0dFuEJiN2CW+NPRYuCK5sj9eEHI=\", false, function () {\n  return [useTheme];\n});\n_c2 = Sidebar;\nexport default Sidebar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Item\");\n$RefreshReg$(_c2, \"Sidebar\");", "map": {"version": 3, "names": ["useState", "useEffect", "ProSidebar", "<PERSON><PERSON>", "MenuItem", "Box", "IconButton", "Typography", "useTheme", "Link", "UserAvatar", "tokens", "HomeOutlinedIcon", "PeopleOutlinedIcon", "ContactsOutlinedIcon", "ReceiptOutlinedIcon", "PersonOutlinedIcon", "TrafficIcon", "LocalShippingIcon", "MonetizationOnIcon", "BarChartOutlinedIcon", "PieChartOutlineOutlinedIcon", "TimelineOutlinedIcon", "MenuOutlinedIcon", "MapOutlinedIcon", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "title", "to", "icon", "selected", "setSelected", "_s", "theme", "colors", "palette", "mode", "fullPath", "startsWith", "active", "style", "color", "grey", "onClick", "children", "textDecoration", "display", "alignItems", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Sidebar", "isSidebar", "isCollapsed", "setIsCollapsed", "_s2", "user", "setUser", "name", "role", "userString", "localStorage", "getItem", "userData", "JSON", "parse", "error", "console", "sx", "background", "primary", "backgroundColor", "padding", "collapsed", "position", "left", "top", "zIndex", "iconShape", "undefined", "margin", "justifyContent", "ml", "variant", "mb", "size", "cursor", "border", "blueAccent", "boxShadow", "tooltipTitle", "textAlign", "fontWeight", "m", "paddingLeft", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/cocal-platforme-test/frontend/src/interfaces/interface-admin/scenes/global/Sidebar.jsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\nimport { ProSidebar, Menu, MenuItem } from \"react-pro-sidebar\";\nimport { Box, IconButton, Typography, useTheme } from \"@mui/material\";\nimport { Link } from \"react-router-dom\";\nimport \"react-pro-sidebar/dist/css/styles.css\";\nimport \"./Sidebar.css\";\nimport UserAvatar from \"../../../../components/common/UserAvatar\";\nimport { tokens } from \"../../theme\";\nimport HomeOutlinedIcon from \"@mui/icons-material/HomeOutlined\";\nimport PeopleOutlinedIcon from \"@mui/icons-material/PeopleOutlined\";\nimport ContactsOutlinedIcon from \"@mui/icons-material/ContactsOutlined\";\nimport ReceiptOutlinedIcon from \"@mui/icons-material/ReceiptOutlined\";\nimport PersonOutlinedIcon from \"@mui/icons-material/PersonOutlined\";\nimport TrafficIcon from \"@mui/icons-material/Traffic\";\nimport LocalShippingIcon from \"@mui/icons-material/LocalShipping\";\nimport MonetizationOnIcon from \"@mui/icons-material/MonetizationOn\";\n\nimport BarChartOutlinedIcon from \"@mui/icons-material/BarChartOutlined\";\nimport PieChartOutlineOutlinedIcon from \"@mui/icons-material/PieChartOutlineOutlined\";\nimport TimelineOutlinedIcon from \"@mui/icons-material/TimelineOutlined\";\nimport MenuOutlinedIcon from \"@mui/icons-material/MenuOutlined\";\nimport MapOutlinedIcon from \"@mui/icons-material/MapOutlined\";\n\nconst Item = ({ title, to, icon, selected, setSelected }) => {\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n  // Préfixer les routes avec /admin\n  const fullPath = to.startsWith('/') ? `/admin${to}` : `/admin/${to}`;\n\n  return (\n    <MenuItem\n      active={selected === title}\n      style={{\n        color: colors.grey[100],\n      }}\n      onClick={() => setSelected(title)}\n      icon={icon}\n    >\n      <Link\n        to={fullPath}\n        style={{\n          textDecoration: 'none',\n          color: 'inherit',\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%',\n          height: '100%'\n        }}\n      >\n        <Typography>{title}</Typography>\n      </Link>\n    </MenuItem>\n  );\n};\n\nconst Sidebar = ({ isSidebar, isCollapsed, setIsCollapsed }) => {\n  const theme = useTheme();\n  const colors = tokens(theme.palette.mode);\n  const [selected, setSelected] = useState(\"Dashboard\");\n  const [user, setUser] = useState({ name: \"Utilisateur\", role: \"admin\" });\n\n  useEffect(() => {\n    // Récupérer les informations de l'utilisateur depuis le localStorage\n    const userString = localStorage.getItem('user');\n    if (userString) {\n      try {\n        const userData = JSON.parse(userString);\n        setUser(userData);\n      } catch (error) {\n        console.error(\"Erreur lors de la récupération des informations utilisateur:\", error);\n      }\n    }\n  }, []);\n\n  return (\n    <Box\n      sx={{\n        \"& .pro-sidebar-inner\": {\n          background: `${colors.primary[400]} !important`,\n        },\n        \"& .pro-icon-wrapper\": {\n          backgroundColor: \"transparent !important\",\n        },\n        \"& .pro-inner-item\": {\n          padding: \"5px 35px 5px 20px !important\",\n        },\n        \"& .pro-inner-item:hover\": {\n          color: \"#38bdf8 !important\",\n        },\n        \"& .pro-menu-item.active\": {\n          color: \"#0ea5e9 !important\",\n        },\n      }}\n    >\n      <ProSidebar collapsed={isCollapsed} style={{ height: '100vh', position: 'fixed', left: 0, top: 0, zIndex: 1000, width: isCollapsed ? '80px' : '250px' }}>\n        <Menu iconShape=\"square\">\n          {/* LOGO AND MENU ICON */}\n          <MenuItem\n            onClick={() => setIsCollapsed(!isCollapsed)}\n            icon={isCollapsed ? <MenuOutlinedIcon /> : undefined}\n            style={{\n              margin: \"10px 0 20px 0\",\n              color: colors.grey[100],\n            }}\n          >\n            {!isCollapsed && (\n              <Box\n                display=\"flex\"\n                justifyContent=\"space-between\"\n                alignItems=\"center\"\n                ml=\"15px\"\n              >\n                <Typography variant=\"h3\" color={colors.grey[100]}>\n                  ADMINIS\n                </Typography>\n                <IconButton onClick={() => setIsCollapsed(!isCollapsed)}>\n                  <MenuOutlinedIcon />\n                </IconButton>\n              </Box>\n            )}\n          </MenuItem>\n\n          {!isCollapsed && (\n            <Box mb=\"25px\">\n              <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\">\n                <UserAvatar\n                  name={user.name}\n                  size={100}\n                  sx={{\n                    cursor: \"pointer\",\n                    border: `2px solid ${colors.blueAccent[500]}`,\n                    boxShadow: `0 4px 12px rgba(56, 189, 248, 0.2)`\n                  }}\n                  tooltipTitle={`${user.name} - ${user.role === 'admin' ? 'Administrateur' :\n                    user.role === 'commercial' ? 'Commercial' :\n                    user.role === 'driver' ? 'Chauffeur' : 'Utilisateur'}`}\n                />\n              </Box>\n              <Box textAlign=\"center\">\n                <Typography\n                  variant=\"h2\"\n                  color={colors.grey[100]}\n                  fontWeight=\"bold\"\n                  sx={{ m: \"10px 0 0 0\" }}\n                >\n                 {user.name || 'Utilisateur'}\n                </Typography>\n                <Typography variant=\"h5\" color={colors.blueAccent[500]}>\n                 {user.role === 'admin' ? 'Administrateur' :\n                  user.role === 'commercial' ? 'Commercial' :\n                  user.role === 'driver' ? 'Chauffeur' : 'Utilisateur'}\n                </Typography>\n              </Box>\n            </Box>\n          )}\n\n          <Box paddingLeft={isCollapsed ? undefined : \"10%\"}>\n            <Item\n              title=\"Dashboard\"\n              to=\"/\"\n              icon={<HomeOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n\n            <Typography\n              variant=\"h6\"\n              color={colors.grey[500]}\n              sx={{ m: \"15px 0 5px 20px\" }}\n            >\n              Data\n            </Typography>\n            <Item\n              title=\"Manage Team\"\n              to=\"/team\"\n              icon={<PeopleOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Contacts Information\"\n              to=\"/contacts\"\n              icon={<ContactsOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Invoices Balances\"\n              to=\"/invoices\"\n              icon={<ReceiptOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n\n            <Typography\n              variant=\"h6\"\n              color={colors.grey[500]}\n              sx={{ m: \"15px 0 5px 20px\" }}\n            >\n              Pages\n            </Typography>\n            <Item\n              title=\"Profile Form\"\n              to=\"/form\"\n              icon={<PersonOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n\n\n            <Typography\n              variant=\"h6\"\n              color={colors.grey[500]}\n              sx={{ m: \"15px 0 5px 20px\" }}\n            >\n              Charts\n            </Typography>\n            <Item\n              title=\"Bar Chart\"\n              to=\"/bar\"\n              icon={<BarChartOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Pie Chart\"\n              to=\"/pie\"\n              icon={<PieChartOutlineOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Montants par Type\"\n              to=\"/line\"\n              icon={<TimelineOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Geography Chart\"\n              to=\"/geography\"\n              icon={<MapOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Gérer les véhicules\"\n              to=\"/vehicles\"\n              icon={<TrafficIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Gérer les chauffeurs\"\n              to=\"/drivers\"\n              icon={<PeopleOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Gérer les commerciaux\"\n              to=\"/commercials\"\n              icon={<PeopleOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Collectes terminées\"\n              to=\"/driver-collections\"\n              icon={<LocalShippingIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Montants par chauffeur\"\n              to=\"/driver-revenue\"\n              icon={<MonetizationOnIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n            <Item\n              title=\"Points de ramassage\"\n              to=\"/points\"\n              icon={<MapOutlinedIcon />}\n              selected={selected}\n              setSelected={setSelected}\n            />\n          </Box>\n        </Menu>\n      </ProSidebar>\n    </Box>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,mBAAmB;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACrE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,uCAAuC;AAC9C,OAAO,eAAe;AACtB,OAAOC,UAAU,MAAM,0CAA0C;AACjE,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,kBAAkB,MAAM,oCAAoC;AAEnE,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,IAAI,GAAGA,CAAC;EAAEC,KAAK;EAAEC,EAAE;EAAEC,IAAI;EAAEC,QAAQ;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAMC,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,MAAM2B,MAAM,GAAGxB,MAAM,CAACuB,KAAK,CAACE,OAAO,CAACC,IAAI,CAAC;EACzC;EACA,MAAMC,QAAQ,GAAGT,EAAE,CAACU,UAAU,CAAC,GAAG,CAAC,GAAG,SAASV,EAAE,EAAE,GAAG,UAAUA,EAAE,EAAE;EAEpE,oBACEH,OAAA,CAACtB,QAAQ;IACPoC,MAAM,EAAET,QAAQ,KAAKH,KAAM;IAC3Ba,KAAK,EAAE;MACLC,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG;IACxB,CAAE;IACFC,OAAO,EAAEA,CAAA,KAAMZ,WAAW,CAACJ,KAAK,CAAE;IAClCE,IAAI,EAAEA,IAAK;IAAAe,QAAA,eAEXnB,OAAA,CAACjB,IAAI;MACHoB,EAAE,EAAES,QAAS;MACbG,KAAK,EAAE;QACLK,cAAc,EAAE,MAAM;QACtBJ,KAAK,EAAE,SAAS;QAChBK,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,eAEFnB,OAAA,CAACnB,UAAU;QAAAsC,QAAA,EAAEjB;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEf,CAAC;AAACrB,EAAA,CA9BIN,IAAI;EAAA,QACMnB,QAAQ;AAAA;AAAA+C,EAAA,GADlB5B,IAAI;AAgCV,MAAM6B,OAAO,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,GAAA;EAC9D,MAAM1B,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,MAAM2B,MAAM,GAAGxB,MAAM,CAACuB,KAAK,CAACE,OAAO,CAACC,IAAI,CAAC;EACzC,MAAM,CAACN,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,WAAW,CAAC;EACrD,MAAM,CAAC6D,IAAI,EAAEC,OAAO,CAAC,GAAG9D,QAAQ,CAAC;IAAE+D,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAQ,CAAC,CAAC;EAExE/D,SAAS,CAAC,MAAM;IACd;IACA,MAAMgE,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,IAAIF,UAAU,EAAE;MACd,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACvCH,OAAO,CAACM,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACtF;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7C,OAAA,CAACrB,GAAG;IACFoE,EAAE,EAAE;MACF,sBAAsB,EAAE;QACtBC,UAAU,EAAE,GAAGvC,MAAM,CAACwC,OAAO,CAAC,GAAG,CAAC;MACpC,CAAC;MACD,qBAAqB,EAAE;QACrBC,eAAe,EAAE;MACnB,CAAC;MACD,mBAAmB,EAAE;QACnBC,OAAO,EAAE;MACX,CAAC;MACD,yBAAyB,EAAE;QACzBnC,KAAK,EAAE;MACT,CAAC;MACD,yBAAyB,EAAE;QACzBA,KAAK,EAAE;MACT;IACF,CAAE;IAAAG,QAAA,eAEFnB,OAAA,CAACxB,UAAU;MAAC4E,SAAS,EAAEpB,WAAY;MAACjB,KAAK,EAAE;QAAES,MAAM,EAAE,OAAO;QAAE6B,QAAQ,EAAE,OAAO;QAAEC,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,MAAM,EAAE,IAAI;QAAEjC,KAAK,EAAES,WAAW,GAAG,MAAM,GAAG;MAAQ,CAAE;MAAAb,QAAA,eACtJnB,OAAA,CAACvB,IAAI;QAACgF,SAAS,EAAC,QAAQ;QAAAtC,QAAA,gBAEtBnB,OAAA,CAACtB,QAAQ;UACPwC,OAAO,EAAEA,CAAA,KAAMe,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C5B,IAAI,EAAE4B,WAAW,gBAAGhC,OAAA,CAACH,gBAAgB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG8B,SAAU;UACrD3C,KAAK,EAAE;YACL4C,MAAM,EAAE,eAAe;YACvB3C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG;UACxB,CAAE;UAAAE,QAAA,EAED,CAACa,WAAW,iBACXhC,OAAA,CAACrB,GAAG;YACF0C,OAAO,EAAC,MAAM;YACduC,cAAc,EAAC,eAAe;YAC9BtC,UAAU,EAAC,QAAQ;YACnBuC,EAAE,EAAC,MAAM;YAAA1C,QAAA,gBAETnB,OAAA,CAACnB,UAAU;cAACiF,OAAO,EAAC,IAAI;cAAC9C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAE;cAAAE,QAAA,EAAC;YAElD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5B,OAAA,CAACpB,UAAU;cAACsC,OAAO,EAAEA,CAAA,KAAMe,cAAc,CAAC,CAACD,WAAW,CAAE;cAAAb,QAAA,eACtDnB,OAAA,CAACH,gBAAgB;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEV,CAACI,WAAW,iBACXhC,OAAA,CAACrB,GAAG;UAACoF,EAAE,EAAC,MAAM;UAAA5C,QAAA,gBACZnB,OAAA,CAACrB,GAAG;YAAC0C,OAAO,EAAC,MAAM;YAACuC,cAAc,EAAC,QAAQ;YAACtC,UAAU,EAAC,QAAQ;YAAAH,QAAA,eAC7DnB,OAAA,CAAChB,UAAU;cACTqD,IAAI,EAAEF,IAAI,CAACE,IAAK;cAChB2B,IAAI,EAAE,GAAI;cACVjB,EAAE,EAAE;gBACFkB,MAAM,EAAE,SAAS;gBACjBC,MAAM,EAAE,aAAazD,MAAM,CAAC0D,UAAU,CAAC,GAAG,CAAC,EAAE;gBAC7CC,SAAS,EAAE;cACb,CAAE;cACFC,YAAY,EAAE,GAAGlC,IAAI,CAACE,IAAI,MAAMF,IAAI,CAACG,IAAI,KAAK,OAAO,GAAG,gBAAgB,GACtEH,IAAI,CAACG,IAAI,KAAK,YAAY,GAAG,YAAY,GACzCH,IAAI,CAACG,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,aAAa;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5B,OAAA,CAACrB,GAAG;YAAC2F,SAAS,EAAC,QAAQ;YAAAnD,QAAA,gBACrBnB,OAAA,CAACnB,UAAU;cACTiF,OAAO,EAAC,IAAI;cACZ9C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAE;cACxBsD,UAAU,EAAC,MAAM;cACjBxB,EAAE,EAAE;gBAAEyB,CAAC,EAAE;cAAa,CAAE;cAAArD,QAAA,EAExBgB,IAAI,CAACE,IAAI,IAAI;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACb5B,OAAA,CAACnB,UAAU;cAACiF,OAAO,EAAC,IAAI;cAAC9C,KAAK,EAAEP,MAAM,CAAC0D,UAAU,CAAC,GAAG,CAAE;cAAAhD,QAAA,EACrDgB,IAAI,CAACG,IAAI,KAAK,OAAO,GAAG,gBAAgB,GACxCH,IAAI,CAACG,IAAI,KAAK,YAAY,GAAG,YAAY,GACzCH,IAAI,CAACG,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG;YAAa;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED5B,OAAA,CAACrB,GAAG;UAAC8F,WAAW,EAAEzC,WAAW,GAAG0B,SAAS,GAAG,KAAM;UAAAvC,QAAA,gBAChDnB,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,WAAW;YACjBC,EAAE,EAAC,GAAG;YACNC,IAAI,eAAEJ,OAAA,CAACd,gBAAgB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEF5B,OAAA,CAACnB,UAAU;YACTiF,OAAO,EAAC,IAAI;YACZ9C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAE;YACxB8B,EAAE,EAAE;cAAEyB,CAAC,EAAE;YAAkB,CAAE;YAAArD,QAAA,EAC9B;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,aAAa;YACnBC,EAAE,EAAC,OAAO;YACVC,IAAI,eAAEJ,OAAA,CAACb,kBAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,sBAAsB;YAC5BC,EAAE,EAAC,WAAW;YACdC,IAAI,eAAEJ,OAAA,CAACZ,oBAAoB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,mBAAmB;YACzBC,EAAE,EAAC,WAAW;YACdC,IAAI,eAAEJ,OAAA,CAACX,mBAAmB;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEF5B,OAAA,CAACnB,UAAU;YACTiF,OAAO,EAAC,IAAI;YACZ9C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAE;YACxB8B,EAAE,EAAE;cAAEyB,CAAC,EAAE;YAAkB,CAAE;YAAArD,QAAA,EAC9B;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,cAAc;YACpBC,EAAE,EAAC,OAAO;YACVC,IAAI,eAAEJ,OAAA,CAACV,kBAAkB;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAGF5B,OAAA,CAACnB,UAAU;YACTiF,OAAO,EAAC,IAAI;YACZ9C,KAAK,EAAEP,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAE;YACxB8B,EAAE,EAAE;cAAEyB,CAAC,EAAE;YAAkB,CAAE;YAAArD,QAAA,EAC9B;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,WAAW;YACjBC,EAAE,EAAC,MAAM;YACTC,IAAI,eAAEJ,OAAA,CAACN,oBAAoB;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,WAAW;YACjBC,EAAE,EAAC,MAAM;YACTC,IAAI,eAAEJ,OAAA,CAACL,2BAA2B;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtCvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,mBAAmB;YACzBC,EAAE,EAAC,OAAO;YACVC,IAAI,eAAEJ,OAAA,CAACJ,oBAAoB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,iBAAiB;YACvBC,EAAE,EAAC,YAAY;YACfC,IAAI,eAAEJ,OAAA,CAACF,eAAe;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,2BAAqB;YAC3BC,EAAE,EAAC,WAAW;YACdC,IAAI,eAAEJ,OAAA,CAACT,WAAW;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtBvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,yBAAsB;YAC5BC,EAAE,EAAC,UAAU;YACbC,IAAI,eAAEJ,OAAA,CAACb,kBAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,0BAAuB;YAC7BC,EAAE,EAAC,cAAc;YACjBC,IAAI,eAAEJ,OAAA,CAACb,kBAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,wBAAqB;YAC3BC,EAAE,EAAC,qBAAqB;YACxBC,IAAI,eAAEJ,OAAA,CAACR,iBAAiB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,wBAAwB;YAC9BC,EAAE,EAAC,iBAAiB;YACpBC,IAAI,eAAEJ,OAAA,CAACP,kBAAkB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACF5B,OAAA,CAACC,IAAI;YACHC,KAAK,EAAC,qBAAqB;YAC3BC,EAAE,EAAC,SAAS;YACZC,IAAI,eAAEJ,OAAA,CAACF,eAAe;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BvB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACM,GAAA,CA7OIJ,OAAO;EAAA,QACGhD,QAAQ;AAAA;AAAA4F,GAAA,GADlB5C,OAAO;AA+Ob,eAAeA,OAAO;AAAC,IAAAD,EAAA,EAAA6C,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}